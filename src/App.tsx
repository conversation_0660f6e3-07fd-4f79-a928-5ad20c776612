import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, RouterProvider } from 'react-router-dom'
import { SWRConfig } from 'swr'
import ErrorPage from './error-page'
import DashboardLayout from './components/DashboardLayout'
import { BreadcrumbLink } from './components/ui/breadcrumb'
import Dashboard from './routes/dashboard'
import AuthPage from './routes/auth'
import { fetcher } from './network/request'
import { Toaster } from './components/ui/toaster'
import UserPage from './routes/user'
import UserDetails from './routes/user/details'
import { AuthProvider } from './hooks/useAuth'
import ActivityPage from './routes/activity'
import ActivityDetails from './routes/activity/details'
import AmenityPage from './routes/amenity'
import AmenityDetails from './routes/amenity/details'
import SessionDetails from './routes/session/details'
import SessionPage from './routes/session'
import ListingFlagPage from './routes/listing-flag'
import ListingFlagDetails from './routes/listing-flag/details'
import ListingPage from './routes/listing'
import ListingDetails from './routes/listing/details'
import InfoContentPage from './routes/info-content'
import InfoContentDetails from './routes/info-content/details'
import PriceMatchRequestDetails from './routes/price-match-request/details'
import PriceMatchRequestPage from './routes/price-match-request'
import ShopBannerPage from './routes/shop-banner'
import ShopBannerDetails from './routes/shop-banner/details'
import EventInfoPage from './routes/event-info'
import EventInfoDetails from './routes/event-info/details'
import DigitalSignageDeviceDetails from './routes/digital-signage-device/details'
import DigitalSignageDevicePage from './routes/digital-signage-device'
import DigitalSignageMediaPage from './routes/digital-signage-media'
import DigitalSignageMediaDetails from './routes/digital-signage-media/details'
// import DigitalSignageDeviceSettingPage from './routes/digital-signage-device-setting'
// import DigitalSignageDeviceSettingDetails from './routes/digital-signage-device-setting/details'
import NotificationMessagePage from './routes/notification-message'
import NotificationMessageDetails from './routes/notification-message/details'
import { createBreadcrumbWithPagination } from './hooks/useBreadcrumbWithPagination'

const router = createBrowserRouter([
  {
    element: <AuthProvider />,
    children: [
      // public
      {
        path: '/',
        element: <AuthPage />,
        errorElement: <ErrorPage />
      },
      {
        element: <DashboardLayout />,
        errorElement: <ErrorPage />,
        handle: {
          crumb: () => (
            <BreadcrumbLink asChild>
              <Link to="/dashboard">Dashboard</Link>
            </BreadcrumbLink>
          )
        },
        children: [
          // common
          { path: '/dashboard', element: <Dashboard /> },
          {
            path: '/users',
            handle: {
              crumb: createBreadcrumbWithPagination('/users', 'Users')
            },
            children: [
              {
                index: true,
                element: <UserPage />
              },
              {
                path: '/users/:user_id',
                element: <UserDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/users/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/activities',
            handle: {
              crumb: createBreadcrumbWithPagination('/activities', 'Activities')
            },
            children: [
              {
                index: true,
                element: <ActivityPage />
              },
              {
                path: '/activities/:activity_id',
                element: <ActivityDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/activities/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/amenities',
            handle: {
              crumb: createBreadcrumbWithPagination('/amenities', 'Amenities')
            },
            children: [
              {
                index: true,
                element: <AmenityPage />
              },
              {
                path: '/amenities/:amenity_id',
                element: <AmenityDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/amenities/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/sessions',
            handle: {
              crumb: createBreadcrumbWithPagination('/sessions', 'Sessions')
            },
            children: [
              {
                index: true,
                element: <SessionPage />
              },
              {
                path: '/sessions/:session_id',
                element: <SessionDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/sessions/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/listings',
            handle: {
              crumb: createBreadcrumbWithPagination('/listings', 'Listings')
            },
            children: [
              {
                index: true,
                element: <ListingPage />
              },
              {
                path: '/listings/:listing_id',
                element: <ListingDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/listings/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/listing-flags',
            handle: {
              crumb: createBreadcrumbWithPagination('/listing-flags', 'Listing Flags')
            },
            children: [
              {
                index: true,
                element: <ListingFlagPage />
              },
              {
                path: '/listing-flags/:listing_flag_id',
                element: <ListingFlagDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/listing-flags/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/pages',
            handle: {
              crumb: createBreadcrumbWithPagination('/pages', 'Pages')
            },
            children: [
              {
                index: true,
                element: <InfoContentPage />
              },
              {
                path: '/pages/:info_slug',
                element: <InfoContentDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/pages/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/price-match-requests',
            handle: {
              crumb: createBreadcrumbWithPagination('/price-match-requests', 'Price Match Requests')
            },
            children: [
              {
                index: true,
                element: <PriceMatchRequestPage />
              },
              {
                path: '/price-match-requests/:id',
                element: <PriceMatchRequestDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/price-match-requests/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/shop-banners',
            handle: {
              crumb: createBreadcrumbWithPagination('/shop-banners', 'Shop Banners')
            },
            children: [
              {
                index: true,
                element: <ShopBannerPage />
              },
              {
                path: '/shop-banners/:id',
                element: <ShopBannerDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/shop-banners/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/announcements',
            handle: {
              crumb: createBreadcrumbWithPagination('/announcements', 'Announcements')
            },
            children: [
              {
                index: true,
                element: <EventInfoPage />
              },
              {
                path: '/announcements/:id',
                element: <EventInfoDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/announcements/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/digital-signage-devices',
            handle: {
              crumb: createBreadcrumbWithPagination('/digital-signage-devices', 'Digital Signage Device')
            },
            children: [
              {
                index: true,
                element: <DigitalSignageDevicePage />
              },
              {
                path: '/digital-signage-devices/:id',
                element: <DigitalSignageDeviceDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/digital-signage-devices/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          {
            path: '/digital-signage-medias',
            handle: {
              crumb: createBreadcrumbWithPagination('/digital-signage-medias', 'Digital Signage Media')
            },
            children: [
              {
                index: true,
                element: <DigitalSignageMediaPage />
              },
              {
                path: '/digital-signage-medias/:id',
                element: <DigitalSignageMediaDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/digital-signage-medias/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          },
          // {
          //   path: '/digital-signage-device-setting',
          //   handle: {
          //     crumb: () => (
          //       <BreadcrumbLink to="/digital-signage-device-setting">
          //         Digital Signage Media
          //       </BreadcrumbLink>
          //     )
          //   },
          //   children: [
          //     {
          //       index: true,
          //       element: <DigitalSignageDeviceSettingPage />
          //     },
          //     {
          //       path: '/digital-signage-device-setting/:id',
          //       element: <DigitalSignageDeviceSettingDetails />,
          //       handle: {
          //         crumb: (params: string) => {
          //           return (
          //             <BreadcrumbLink to={`/digital-signage-device-setting/${params}`}>
          //               {params}
          //             </BreadcrumbLink>
          //           )
          //         }
          //       }
          //     }
          //   ]
          // },
          {
            path: '/notification-messages',
            handle: {
              crumb: createBreadcrumbWithPagination('/notification-messages', 'Notification Messages')
            },
            children: [
              {
                index: true,
                element: <NotificationMessagePage />
              },
              {
                path: '/notification-messages/:id',
                element: <NotificationMessageDetails />,
                handle: {
                  crumb: (params: string) => (
                    <BreadcrumbLink asChild>
                      <Link to={`/notification-messages/${params}`}>{params}</Link>
                    </BreadcrumbLink>
                  )
                }
              }
            ]
          }
        ]
      }
    ]
  }
])

function App() {
  return (
    <SWRConfig value={{ fetcher, revalidateOnFocus: false, errorRetryCount: 1 }}>
      <RouterProvider router={router} />
      <Toaster />
    </SWRConfig>
  )
}

export default App
