import { useEffect, useRef } from 'react'

/**
 * A hook to set the document title
 * @param title The title to set
 * @param includeAppName Whether to include the app name in the title
 */
export const useDocumentTitle = (title: string, includeAppName = true) => {
  // Use a ref to track if this is the first render
  // This ensures the hook is always called the same number of times
  const isMounted = useRef(false)

  // Always call useEffect, even if title is undefined
  useEffect(() => {
    // Set isMounted to true on first render
    if (!isMounted.current) {
      isMounted.current = true
    }

    // Only update the title if we have a valid title
    if (title) {
      const appName = 'GoMama'
      const formattedTitle = includeAppName ? `${title} | ${appName}` : title
      document.title = formattedTitle

      // Restore the original title when the component unmounts
      return () => {
        document.title = appName
      }
    }
  }, [title, includeAppName])
}
