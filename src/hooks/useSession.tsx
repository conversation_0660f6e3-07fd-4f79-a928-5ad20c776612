import { serialize } from '@/network/request'
import SessionService from '@/network/services/session'
import useSWR from 'swr'

export const useSession = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(SessionService.getSingle(id), {}))
  const session = data?.data

  const serializedSession = session && SessionService.serialize(session)

  return {
    session: serializedSession,
    isLoading,
    error
  }
}
