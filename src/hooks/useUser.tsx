import { serialize } from '@/network/request'
import UserService from '@/network/services/user'
import useSWR from 'swr'

export const useUser = (userId: string | number) => {
  const { data, error, isLoading } = useSWR(serialize(UserService.getUser(userId), {}))
  const user = data?.data

  // Serialize user for DateTime
  const serializedUser = user && UserService.serialize(user)

  return {
    user: serializedUser,
    isLoading,
    error
  }
}
