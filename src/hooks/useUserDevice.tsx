import { serialize } from '@/network/request'
import UserDeviceService from '@/network/services/userDevice'
import useSWR from 'swr'

export const useUserDevice = (id: string | number) => {
  const { data, error, isLoading } = useSWR(serialize(UserDeviceService.getDevice(id), {}))
  const device = data?.data

  // Serialize device for DateTime
  const serializedDevice = device && UserDeviceService.serialize(device)

  return {
    device: serializedDevice,
    isLoading,
    error
  }
}
