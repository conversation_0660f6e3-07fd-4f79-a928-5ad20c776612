import { serialize } from '@/network/request'
import PriceMatchRequestService from '@/network/services/priceMatchRequest'
import useSWR from 'swr'

export const usePriceMatchRequest = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(PriceMatchRequestService.getSingle(id), { include_user: true }))
  const priceMatchRequest = data?.data

  return {
    priceMatchRequest,
    isLoading,
    error
  }
}
