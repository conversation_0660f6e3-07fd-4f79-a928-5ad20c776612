import { serialize } from '@/network/request'
import AmenityService from '@/network/services/amenity'
import { Amenity } from '@/types/Amenity'

import useSWR from 'swr'

export const useSearchAmenity = (query?: string) => {
  const { data, error, isLoading } = useSWR(serialize(AmenityService.getAll, { name: query }), {
    keepPreviousData: true
  })

  const amenities = data?.data
  const pagination = AmenityService.toPaginate(data)

  const serializedAmenities = amenities?.map((amenity: Amenity) => AmenityService.serialize(amenity))

  return {
    data: serializedAmenities,
    pagination,
    isLoading,
    error
  }
}
