import { serialize } from '@/network/request'
import UserService from '@/network/services/user'
import { User } from '@/types/User'
import useSWR from 'swr'

export const useSearchUser = (query?: string) => {
  const { data, error, isLoading } = useSWR(serialize(UserService.getUsers, { name: query }), {
    keepPreviousData: true
  })

  const users = data?.data
  const pagination = UserService.toPaginate(data)

  const serializedUsers = users?.map((user: User) => UserService.serialize(user))

  return {
    data: serializedUsers,
    pagination,
    isLoading,
    error
  }
}
