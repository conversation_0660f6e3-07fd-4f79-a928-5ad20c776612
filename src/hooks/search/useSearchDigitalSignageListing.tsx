import { serialize } from '@/network/request'
import DigitalSignageListingService from '@/network/services/digitalSignageListing'
import { DigitalSignageListing } from '@/types/DigitalSignageListing'
import useSWR from 'swr'

export const useSearchDigitalSignageListing = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(DigitalSignageListingService.getAll, { name: query }),
    {
      keepPreviousData: true
    }
  )

  const listings = data?.data
  const pagination = DigitalSignageListingService.toPaginate(data)

  const serializedListings = listings?.map((listing: DigitalSignageListing) =>
    DigitalSignageListingService.serialize(listing)
  )

  return {
    data: serializedListings,
    pagination,
    isLoading,
    error
  }
}
