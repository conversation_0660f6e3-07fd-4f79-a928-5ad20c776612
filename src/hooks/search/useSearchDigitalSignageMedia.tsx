import { serialize } from '@/network/request'
import DigitalSignageMediaService from '@/network/services/digitalSignageMedia'
import { DigitalSignageMedia } from '@/types/DigitalSignageMedia'
import useSWR from 'swr'

export const useSearchDigitalSignageMedia = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(DigitalSignageMediaService.getAll, { name: query }),
    {
      keepPreviousData: true
    }
  )

  const mediaItems = data?.data
  const pagination = DigitalSignageMediaService.toPaginate(data)

  const serializedMediaItems = mediaItems?.map((media: DigitalSignageMedia) =>
    DigitalSignageMediaService.serialize(media)
  )

  return {
    data: serializedMediaItems,
    pagination,
    isLoading,
    error
  }
}
