import { serialize } from '@/network/request'
import DigitalSignageDeviceSettingService from '@/network/services/digitalSignageDeviceSetting'
import { DigitalSignageDeviceSetting } from '@/types/DigitalSignageDeviceSetting'
import useSWR from 'swr'

export const useSearchDigitalSignageDeviceSetting = (query?: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(DigitalSignageDeviceSettingService.getAll, { name: query }),
    {
      keepPreviousData: true
    }
  )

  const settings = data?.data
  const pagination = DigitalSignageDeviceSettingService.toPaginate(data)

  const serializedSettings = settings?.map((setting: DigitalSignageDeviceSetting) =>
    DigitalSignageDeviceSettingService.serialize(setting)
  )

  return {
    data: serializedSettings,
    pagination,
    isLoading,
    error
  }
}
