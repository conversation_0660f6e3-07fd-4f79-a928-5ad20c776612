import { serialize } from '@/network/request'
import RegionService from '@/network/services/region'
import { Region } from '@/types/Region'

import useSWR from 'swr'

export const useSearchRegion = (query?: string) => {
  const { data, error, isLoading } = useSWR(serialize(RegionService.getAll, { name: query }), {
    keepPreviousData: true
  })

  const regions = data?.data
  const pagination = RegionService.toPaginate(data)

  const serializedRegions = regions?.map((region: Region) => RegionService.serialize(region))

  return {
    data: serializedRegions,
    pagination,
    isLoading,
    error
  }
}
