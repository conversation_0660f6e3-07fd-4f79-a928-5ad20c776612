import { serialize } from '@/network/request'
import ListingFileService from '@/network/services/listingFile'
import { ListingFile } from '@/types/ListingFile'

import useSWR from 'swr'

export const useSearchListingFile = (query?: string) => {
  const { data, error, isLoading } = useSWR(serialize(ListingFileService.getAll, { name: query }), {
    keepPreviousData: true
  })

  const listingFile = data?.data
  const pagination = ListingFileService.toPaginate(data)

  const serializedListingFile = listingFile?.map((file: ListingFile) => ListingFileService.serialize(file))

  return {
    data: serializedListingFile,
    pagination,
    isLoading,
    error
  }
}
