import { serialize } from '@/network/request'
import ActivityService from '@/network/services/activity'
import { Activity } from '@/types/Activity'

import useSWR from 'swr'

export const useSearchActivity = (query?: string) => {
  const { data, error, isLoading } = useSWR(serialize(ActivityService.getAll, { name: query }), {
    keepPreviousData: true
  })

  const activities = data?.data
  const pagination = ActivityService.toPaginate(data)

  const serializedActivities = activities?.map((activity: Activity) => ActivityService.serialize(activity))

  return {
    data: serializedActivities,
    pagination,
    isLoading,
    error
  }
}
