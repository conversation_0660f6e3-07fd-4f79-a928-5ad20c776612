import { serialize } from '@/network/request'
import ListingTypeService from '@/network/services/listingType'
import { ListingType } from '@/types/ListingType'

import useSWR from 'swr'

export const useSearchListingType = (query?: string) => {
  const { data, error, isLoading } = useSWR(serialize(ListingTypeService.getAll, { name: query }), {
    keepPreviousData: true
  })

  const listingTypes = data?.data
  const pagination = ListingTypeService.toPaginate(data)

  const serializedListingTypes = listingTypes?.map((listingType: ListingType) => ListingTypeService.serialize(listingType))

  return {
    data: serializedListingTypes,
    pagination,
    isLoading,
    error
  }
}
