import { serialize } from '@/network/request'
import RegionService from '@/network/services/region'
import useSWR from 'swr'

export const useRegion = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(RegionService.getSingle(id), {}))
  const region = data?.data

  const serializedRegion = region && RegionService.serialize(region)

  return {
    region: serializedRegion,
    isLoading,
    error
  }
}
