import { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { PaginationState } from '@tanstack/react-table'

interface UseTableStateWithUrlProps {
  defaultPageIndex?: number
  defaultPageSize?: number
}

/**
 * A hook that synchronizes table pagination state with URL query parameters
 * @param defaultPageIndex The default page index to use if no query parameter is present
 * @param defaultPageSize The default page size to use if no query parameter is present
 * @returns An object with the current pagination state and a function to update it
 */
export function useTableStateWithUrl({
  defaultPageIndex = 0,
  defaultPageSize = 20
}: UseTableStateWithUrlProps = {}) {
  const location = useLocation()
  const navigate = useNavigate()

  // Initialize with the query parameters from URL or default values
  const getInitialPaginationState = (): PaginationState => {
    const searchParams = new URLSearchParams(location.search)

    const pageIndex = searchParams.get('page')
    const pageSize = searchParams.get('size')

    return {
      pageIndex: pageIndex ? parseInt(pageIndex, 10) : defaultPageIndex,
      pageSize: pageSize ? parseInt(pageSize, 10) : defaultPageSize
    }
  }

  const [paginationState, setPaginationState] = useState<PaginationState>(
    getInitialPaginationState()
  )

  // Update the URL query parameters when the pagination state changes
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)

    // Only update if the values are different from the current query parameters
    const currentPageIndex = searchParams.get('page')
    const currentPageSize = searchParams.get('size')

    let shouldUpdate = false

    if (paginationState.pageIndex.toString() !== currentPageIndex) {
      searchParams.set('page', paginationState.pageIndex.toString())
      shouldUpdate = true
    }

    if (paginationState.pageSize.toString() !== currentPageSize) {
      searchParams.set('size', paginationState.pageSize.toString())
      shouldUpdate = true
    }

    if (shouldUpdate) {
      // Update the URL without adding a new history entry
      navigate(`${location.pathname}?${searchParams.toString()}${location.hash}`, { replace: true })
    }
  }, [paginationState, location, navigate])

  // Listen for changes in the URL query parameters
  useEffect(() => {
    const handleUrlChange = () => {
      const searchParams = new URLSearchParams(location.search)
      const pageIndex = searchParams.get('page')
      const pageSize = searchParams.get('size')

      if (pageIndex || pageSize) {
        setPaginationState({
          pageIndex: pageIndex ? parseInt(pageIndex, 10) : defaultPageIndex,
          pageSize: pageSize ? parseInt(pageSize, 10) : defaultPageSize
        })
      }
    }

    // Call once to initialize
    handleUrlChange()

    // No need for event listener as React Router will re-render on location change
  }, [location.search, defaultPageIndex, defaultPageSize])

  return {
    paginationState,
    setPaginationState
  }
}
