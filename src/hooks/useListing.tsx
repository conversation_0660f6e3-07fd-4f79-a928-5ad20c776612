import { serialize } from '@/network/request'
import ListingService from '@/network/services/listing'
import SessionService from '@/network/services/session'
import { Listing } from '@/types/Listing'
import { Session, SessionWithUserPreloaded } from '@/types/Session'
import useSWR from 'swr'

export const useListing = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(ListingService.getSingle(id), {}))
  const listing = data?.data

  let serializedListing: Listing = listing && ListingService.serialize(listing)
  if (serializedListing) {
    serializedListing.sessions = serializedListing.sessions?.map(
      (session: Session) => SessionService.serialize(session) as SessionWithUserPreloaded
    )
  }

  return {
    listing: serializedListing,
    isLoading,
    error
  }
}
