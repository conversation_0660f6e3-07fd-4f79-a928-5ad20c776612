import { serialize } from '@/network/request'
import NotificationMessageService from '@/network/services/notificationMessage'
import useSWR from 'swr'

export const useNotificationMessage = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(NotificationMessageService.getNotificationMessage(id), {}))
  const notificationMessage = data?.data

  const serializedNotificationMessage = notificationMessage && NotificationMessageService.serialize(notificationMessage)

  return {
    notificationMessage: serializedNotificationMessage,
    isLoading,
    error
  }
}
