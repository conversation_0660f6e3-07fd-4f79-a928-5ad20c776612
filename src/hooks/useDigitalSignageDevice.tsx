import { serialize } from '@/network/request'
import DigitalSignageDeviceService from '@/network/services/digitalSignageDevice'
import useSWR from 'swr'

export const useDigitalSignageDevice = (id: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(DigitalSignageDeviceService.getSingle(id), {})
  )
  const device = data?.data

  const serializedDevice = device && DigitalSignageDeviceService.serialize(device)

  return {
    digitalSignageDevice: serializedDevice,
    isLoading,
    error
  }
}
