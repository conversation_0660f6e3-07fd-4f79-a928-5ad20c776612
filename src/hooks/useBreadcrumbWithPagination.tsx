import BreadcrumbWithPagination from '@/components/Breadcrumb/BreadcrumbWithPagination'

/**
 * A hook that creates breadcrumb links with pagination state preserved
 * @param path The base path for the breadcrumb link
 * @param label The label to display in the breadcrumb
 * @returns A breadcrumb link component with pagination state preserved
 */
export function useBreadcrumbWithPagination(path: string, label: string) {
  // Return the BreadcrumbWithPagination component directly
  // This avoids using hooks in this function
  return <BreadcrumbWithPagination path={path} label={label} />
}

/**
 * A factory function that creates a breadcrumb function for use in router configuration
 * @param path The base path for the breadcrumb link
 * @param label The label to display in the breadcrumb
 * @returns A function that returns a breadcrumb link component with pagination state preserved
 */
export function createBreadcrumbWithPagination(path: string, label: string) {
  // Return a function that returns the BreadcrumbWithPagination component
  // This avoids using hooks in the returned function
  return () => <BreadcrumbWithPagination path={path} label={label} />
}
