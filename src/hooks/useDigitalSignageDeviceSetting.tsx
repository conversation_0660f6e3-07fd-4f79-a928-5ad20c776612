import { serialize } from '@/network/request'
import DigitalSignageDeviceSettingService from '@/network/services/digitalSignageDeviceSetting'
import useSWR from 'swr'

export const useDigitalSignageDeviceSetting = (id: string) => {
  const { data, error, isLoading } = useSWR(
    serialize(DigitalSignageDeviceSettingService.getSingle(id), {})
  )
  const deviceSetting = data?.data

  const serializedDeviceSetting = deviceSetting && DigitalSignageDeviceSettingService.serialize(deviceSetting)

  return {
    digitalSignageDeviceSetting: serializedDeviceSetting,
    isLoading,
    error
  }
}
