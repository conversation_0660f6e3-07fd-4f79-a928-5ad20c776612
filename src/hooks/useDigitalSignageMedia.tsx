import { serialize } from '@/network/request'
import DigitalSignageMediaService from '@/network/services/digitalSignageMedia'
import useSWR from 'swr'

export const useDigitalSignageMedia = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(DigitalSignageMediaService.getSingle(id), {}))
  const media = data?.data

  const serializedMedia = media && DigitalSignageMediaService.serialize(media)

  return {
    digitalSignageMedia: serializedMedia,
    isLoading,
    error
  }
}
