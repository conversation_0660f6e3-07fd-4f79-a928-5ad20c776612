import { serialize } from '@/network/request'
import InfoContentService from '@/network/services/infoContent'
import useSWR from 'swr'

export const useInfoContent = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(InfoContentService.getSingle(id), {}))
  const infoContent = data?.data

  const serializedInfoContent = infoContent && InfoContentService.serialize(infoContent)

  return {
    infoContent: serializedInfoContent,
    isLoading,
    error
  }
}
