import { serialize } from '@/network/request'
import ShopBannerService from '@/network/services/shopBanner'
import useSWR from 'swr'

export const useShopBanner = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(ShopBannerService.getSingle(id), {}))
  const shopBanner = data?.data

  const serializedShopBanner = shopBanner && ShopBannerService.serialize(shopBanner)

  return {
    shopBanner: serializedShopBanner,
    isLoading,
    error
  }
}
