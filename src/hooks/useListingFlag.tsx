import { serialize } from '@/network/request'
import ListingFlagService from '@/network/services/listingFlag'
import useSWR from 'swr'

export const useListingFlag = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(ListingFlagService.getSingle(id), {}))
  const listingFlag = data?.data

  const serializedListingFlag = listingFlag && ListingFlagService.serialize(listingFlag)

  return {
    listingFlag: serializedListingFlag,
    isLoading,
    error
  }
}
