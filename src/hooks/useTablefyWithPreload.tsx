import useSWR, { preload } from 'swr'
import { useEffect } from 'react'
import { serialize, fetcher as defaultFetcher } from '@/network/request'
import { combineObjectAndArray } from '@/lib/utils'

export type TablefyWithPreloadType<T, TResponseType> = {
  swrService: string
  toRow: (data: TResponseType | undefined) => T[]
  toPaginate: (data: TResponseType | undefined) => {
    total: number
    lastPage: number
  }
  pageParam?: string
  limitParam?: string
  pageIndex: number
  pageSize: number
  sortParam?: string
  sorting?: { id: string; desc: boolean }[]
  filters?: any[]
  fetcher?: (...args: any[]) => Promise<TResponseType>
}

export const useTablefyWithPreload = <T, TResponseType>(
  props: TablefyWithPreloadType<T, TResponseType>
) => {
  const {
    swrService,
    toRow,
    toPaginate,
    pageParam = 'page',
    limitParam = 'limit',
    pageIndex,
    pageSize,
    sortParam,
    sorting = [],
    filters = [],
    fetcher
  } = props

  // Current page data
  const { data, error, isLoading, mutate } = useSWR<TResponseType>(
    swrService,
    fetcher || defaultFetcher
  )

  // Preload next page when current page data is loaded
  useEffect(() => {
    if (!isLoading && !error && data) {
      const { lastPage } = toPaginate(data)

      // Only preload if there is a next page
      if (pageIndex < lastPage - 1) {
        const nextPageIndex = pageIndex + 1
        const nextPageUrl = serialize(
          swrService.split('?')[0], // Base URL without query params
          combineObjectAndArray(
            {
              [limitParam]: pageSize,
              [pageParam]: pageParam === 'offset' ? nextPageIndex * pageSize : nextPageIndex + 1,
              ...(sortParam &&
                sorting.length > 0 && {
                  [sortParam]: `${sorting[0].id}:${sorting[0].desc ? 'DESC' : 'ASC'}`
                })
            },
            filters
          )
        )

        // Preload the next page
        preload(nextPageUrl, fetcher || defaultFetcher)
      }
    }
  }, [
    data,
    error,
    isLoading,
    pageIndex,
    pageParam,
    pageSize,
    swrService,
    toPaginate,
    limitParam,
    sortParam,
    sorting,
    filters,
    fetcher
  ])

  const processedData = toRow(data)
  const totalRow = toPaginate(data).total
  const totalPage = toPaginate(data).lastPage

  return {
    tableData: processedData,
    totalRow,
    totalPage,
    isLoading,
    error,
    mutate
  }
}
