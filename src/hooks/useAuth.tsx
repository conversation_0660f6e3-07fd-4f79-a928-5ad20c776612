import { createContext, useContext, useEffect, useMemo, useState } from 'react'
import { useNavigate, useLocation, Outlet } from 'react-router-dom'
import AuthService, { LoginState } from '@/network/services/auth'
import { User } from '@/types/User'

interface AuthContextType {
  user?: User
  role?: 'super_admin' | 'admin'
  loading: boolean
  error?: unknown
  login: (data: LoginState) => void
  //   signUp: (email: string, name: string, password: string) => void
  logout: () => void
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType)

export function AuthProvider(): JSX.Element {
  const [user, setUser] = useState<User>()
  const [role, setRole] = useState<'super_admin' | 'admin'>()
  const [error, setError] = useState<unknown>()
  const [loading, setLoading] = useState<boolean>(false)
  const [loadingInitial, setLoadingInitial] = useState<boolean>(true)

  const navigate = useNavigate()
  const location = useLocation()

  // Reset the error state if we change page
  useEffect(() => {
    if (error) setError(undefined)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname])

  // Check if there is a currently active session
  // when the provider is mounted for the first time.
  //
  // If there is an error, it means there is no session.
  //
  // Finally, just signal the component that the initial load
  // is over.
  useEffect(() => {
    AuthService.clientFindMyself()
      .then(({ data: user }) => {
        setUser(user.data)
        setRole(user.data?.role ?? 'admin') // TODO: hardcode all to admin for now
      })
      .catch((_error) => {
        console.error(_error)
      })
      .finally(() => setLoadingInitial(false))
  }, [])

  // Flags the component loading state and posts the login
  // data to the server.
  //
  // An error means that the email/password combination is
  // not valid.
  //
  // Finally, just signal the component that loading the
  // loading state is over.
  const login = (data: LoginState) => {
    setLoading(true)

    AuthService.login(data)
      .then(({ data }) => {
        setUser(data.data.user)
        setRole(data.data.user?.role ?? 'admin') // TODO: hardcode all to admin for now

        localStorage.setItem('token', data.data.token.token)

        navigate('/dashboard')
      })
      .catch((error) => setError(error))
      .finally(() => setLoading(false))
  }

  // Sends sign up details to the server. On success we just apply
  // the created user to the state.
  //   function signUp(email: string, name: string, password: string) {
  //     setLoading(true)

  //     AuthService
  //       .signUp({ email, name, password })
  //       .then((user) => {
  //         setUser(user)
  //         navigate("/")
  //       })
  //       .catch((error) => setError(error))
  //       .finally(() => setLoading(false))
  //   }

  // Call the logout endpoint and then remove the user
  // from the state.
  function logout() {
    AuthService.logout().then(() => {
      setUser(undefined)
      setRole(undefined)
    })
  }

  // Make the provider update only when it should.
  // We only want to force re-renders if the user,
  // loading or error states change.
  //
  // Whenever the `value` passed into a provider changes,
  // the whole tree under the provider re-renders, and
  // that can be very costly! Even in this case, where
  // you only get re-renders when logging in and out
  // we want to keep things very performant.
  const memoedValue = useMemo(
    () => ({
      user,
      role,
      loading,
      error,
      login,
      //   signUp,
      logout
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [user, loading, error]
  )

  // We only want to render the underlying app after we
  // assert for the presence of a current user.
  return (
    <AuthContext.Provider value={memoedValue}>{!loadingInitial && <Outlet />}</AuthContext.Provider>
  )
}

// Let's only export the `useAuth` hook instead of the context.
// We only want to use the hook directly and never the context component.
// eslint-disable-next-line react-refresh/only-export-components
export default function useAuth() {
  return useContext(AuthContext)
}
