import { serialize } from '@/network/request'
import ActivityService from '@/network/services/activity'
import useSWR from 'swr'

export const useActivity = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(ActivityService.getSingle(id), {}))
  const activity = data?.data

  const serializedActivity = activity && ActivityService.serialize(activity)

  return {
    activity: serializedActivity,
    isLoading,
    error
  }
}
