import { serialize } from '@/network/request'
import AmenityService from '@/network/services/amenity'
import useSWR from 'swr'

export const useAmenity = (id: string) => {
  const { data, error, isLoading } = useSWR(serialize(AmenityService.getSingle(id), {}))
  const amenity = data?.data

  const serializedAmenity = amenity && AmenityService.serialize(amenity)

  return {
    amenity: serializedAmenity,
    isLoading,
    error
  }
}
