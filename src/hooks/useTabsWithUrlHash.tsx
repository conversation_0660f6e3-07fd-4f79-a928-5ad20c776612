import { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

interface UseTabsWithUrlHashProps {
  defaultValue: string
  tabValues: string[]
}

/**
 * A hook that synchronizes tab state with URL hash for navigation
 * @param defaultValue The default tab value to use if no hash is present
 * @param tabValues An array of valid tab values
 * @returns An object with the current value and a function to change the value
 */
export function useTabsWithUrlHash({ defaultValue, tabValues }: UseTabsWithUrlHashProps) {
  const location = useLocation()
  const navigate = useNavigate()
  
  // Initialize with the hash from URL or default value
  const getInitialValue = () => {
    // Get the hash without the # symbol
    const hash = location.hash.replace('#', '')
    
    // Check if the hash is a valid tab value
    if (hash && tabValues.includes(hash)) {
      return hash
    }
    
    return defaultValue
  }
  
  const [value, setValue] = useState<string>(getInitialValue())
  
  // Update the URL hash when the tab changes
  useEffect(() => {
    // Only update if the value is different from the current hash
    const currentHash = location.hash.replace('#', '')
    if (value !== currentHash) {
      // Update the URL without adding a new history entry
      navigate(`${location.pathname}${location.search}#${value}`, { replace: true })
    }
  }, [value, location, navigate])
  
  // Listen for hash changes in the URL
  useEffect(() => {
    const handleHashChange = () => {
      const hash = location.hash.replace('#', '')
      if (hash && tabValues.includes(hash)) {
        setValue(hash)
      }
    }
    
    // Set up event listener for hash changes
    window.addEventListener('hashchange', handleHashChange)
    
    // Clean up
    return () => {
      window.removeEventListener('hashchange', handleHashChange)
    }
  }, [location.hash, tabValues])
  
  return {
    value,
    setValue
  }
}
