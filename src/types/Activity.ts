import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

export type Activity = {
  id: string
  name: string
  description: string
  image_url: string
  is_hidden: boolean
  created_at: DateTime
  updated_at: DateTime
  slug: string
}

export type CreateActivity = {
  name: string
  description: string
  image_file: File
  image_url: string
}

export interface ActivityResponse extends IDataResponse<Activity> {
  data: Array<Activity>
}
