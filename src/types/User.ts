import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'
import { UserDevice } from './UserDevice'

export type User = {
  id: string
  type_id: string
  position_id: string
  username: string
  email_address: string
  full_name: string
  first_name: string
  last_name: string
  photo_url: string
  share_code: string
  gender: string
  birthday: DateTime
  children_birthday: DateTime[]
  mobile_number: string
  company_name: string
  country_name: string
  country_code: string
  country_dial_code: string
  niationality: string
  nric: string
  singpass_mobile_number: string
  timezone: string
  auth_provider: string
  is_admin_verified: boolean
  is_email_address_verified: boolean
  is_mobile_number_verified: boolean
  is_singpass_verified: boolean
  passport_number: string
  is_passport_verified: boolean
  is_gomama_verified: boolean
  is_hidden: boolean
  firestore_id: string
  created_at: DateTime
  updated_at: DateTime
  full_mobile_number: string
  full_singpass_mobile_number: string
  latest_verify_selfie_fail_count: number
  deleted_at?: DateTime
  delete_reason?: string
  devices: UserDevice[]
}

export type CreateUserInput = {
  email_address: string
  full_name: string
  password: string
}

export interface UserResponse extends IDataResponse<User> {
  data: Array<User>
}
