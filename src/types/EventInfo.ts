import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

export enum EventCategoryTag {
  newPods = 'new_pods',
  latestNews = 'latest_news',
  events = 'events',
}

export type EventInfo = {
  id: number
  thumbnail: string
  title: string
  description: string
  content: string
  category_tag: EventCategoryTag
  created_by: string
  updated_by: string
  created_at: DateTime
  updated_at: DateTime
}

export type CreateEventInfo = {
  image_file: File
  title: string
  description: string
  content: string
  category_tag: EventCategoryTag
}

export type UpdateEventInfo = {
  image_file?: File
  title?: string
  description?: string
  content?: string
  category_tag?: EventCategoryTag
}

export interface EventInfoResponse extends IDataResponse<EventInfo> {
  data: Array<EventInfo>
}
