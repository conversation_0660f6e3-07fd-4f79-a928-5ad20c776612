import { DateTime } from 'luxon'
import { IDataResponse } from '@/network/request'

interface Listing {
  id: number
  gomama_core_listing_id: string
  firestore_id: string
  created_at: DateTime
  updated_at: DateTime
}

interface PreloadedDeviceSetting {
  id: string
  name: string
  created_by: string
  updated_by: string
  media_id: number
  created_at: DateTime
  updated_at: DateTime
}

export type DigitalSignageDevice = {
  id: number
  name: string
  firestore_id: string
  listing_id: number
  device_setting_id: number
  created_by: string
  updated_by: string
  listing?: Listing
  device_setting?: PreloadedDeviceSetting
  created_at: DateTime
  updated_at: DateTime
}

export type CreateDigitalSignageDevice = {
  device_setting_id?: number
  name: string
  firestore_id: string
  listing_id: number
}

export interface DigitalSignageDeviceResponse extends IDataResponse<DigitalSignageDevice> {
  data: Array<DigitalSignageDevice>
}
