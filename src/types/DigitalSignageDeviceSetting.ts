import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

interface PreloadedMedia {
  id: number
  media_url: string
  name: string
  type: string
  created_by: string
  updated_by: string
  created_at: DateTime
  updated_at: DateTime
}

interface PreloadedDevice {
  id: number
  name: string
  firestore_id: string
  listing_id: string
  device_setting_id: number
  created_by: string
  updated_by: string
  created_at: DateTime
  updated_at: DateTime
}

export type DigitalSignageDeviceSetting = {
  id: number
  name: string
  created_by: string
  updated_by: string
  media_id: number
  created_at: DateTime
  updated_at: DateTime
  media: PreloadedMedia
  devices: PreloadedDevice[]
}

export type CreateDigitalSignageDeviceSetting = {
  id: number
  name: string
  created_by: string
  updated_by: string
  media_id: number
  created_at: DateTime
  updated_at: DateTime
  media: PreloadedMedia
  devices: PreloadedDevice[]
}

export interface DigitalSignageDeviceSettingResponse
  extends IDataResponse<DigitalSignageDeviceSetting> {
  data: Array<DigitalSignageDeviceSetting>
}
