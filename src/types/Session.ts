import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'
import { User } from './User'
import { Listing } from './Listing'
import { ListingRatings } from './ListingRatings'

export type Session = {
  id: string
  listing_id: string
  user_id: string
  lock_bluetooth_guest_key: string
  lock_custom_pin: string
  lock_daily_pin: string
  lock_hourly_pin: string
  lock_one_time_pin: string
  actual_usage_duration: string
  actual_ended_at?: DateTime
  usage_duration: string
  started_at: DateTime
  expected_ended_at: DateTime
  number_of_usage_extensions: number
  is_ended: boolean
  is_hidden: boolean
  is_usage_extended: boolean
  firestore_id: string
  created_at: DateTime
  updated_at: DateTime
  user?: User
  listing?: Listing
  listing_rating?: ListingRatings
}

export type SessionWithUserPreloaded = Session & {
  user: User
}

export type UpdateSession = {
  is_hidden: boolean
}

export interface SessionResponse extends IDataResponse<Session> {
  data: Array<Session>
}
