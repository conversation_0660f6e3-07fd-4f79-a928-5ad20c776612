import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'
import { User } from './User'

export type NotificationMessage = {
  id: number
  title: string
  message: string
  image_url: string
  create_user: User
  update_user?: User
  created_at: DateTime
  updated_at: DateTime
}

export type CreateNotificationMessageInput = {
  message: string
  title: string
  image_file?: File
  image_url?: string
}

export type UpdateNotificationMessageInput = {
  message?: string
  title?: string
  image_file?: File
  image_url?: string
}

export interface NotificationMessageResponse extends IDataResponse<NotificationMessage> {
  data: Array<NotificationMessage>
}
