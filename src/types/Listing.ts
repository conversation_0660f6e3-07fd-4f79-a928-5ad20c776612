import { DateTime } from 'luxon'
import { ListingFile } from './ListingFile'
import { Position } from './Position'
import { IDataResponse } from '@/network/request'
import { Amenity } from './Amenity'
import { Activity } from './Activity'
import { ListingRatings } from './ListingRatings'
import { Region } from './Region'
import { SessionWithUserPreloaded } from './Session'
// import { Amenity } from './Amenity'

export enum ListingStatus {
  idle = 'idle',
  occupied = 'occupied',
  disinfecting = 'disinfecting'
}

export type Listing = {
  id: string
  position_id: string
  region_id: string
  type_id: string
  verified_by: string
  sugessted_by: string
  name: string
  listing_type: string
  company_name: string
  address_name: string
  description: string
  full_address: string
  contact_number: string
  keywords: string[]
  postal_code: string
  usage_durations: number[]
  usage_extension_durations: number[]
  max_number_of_usage_extensions: number
  number_of_private_feeding_rooms: number
  number_of_diaper_changing_mats: number
  diaper_changing_mat_type: string
  country_dial_code: string
  humidity: string
  temperature: string
  opening_hours: string
  pi_id: string
  pi_last_updated: string
  api_key: string
  lock_id: string
  lock_master_pin: string
  lock_bluetooth_admin_key: string
  door_is_lockable: boolean
  status: ListingStatus
  is_usage_extendable: boolean
  is_verified: boolean
  is_hidden: boolean
  firestore_id: string
  note: string
  created_at: DateTime
  updated_at: DateTime
  listing_files: ListingFile[]
  position: Position
  average_experience_ratings: number
  total_experience_ratings: number
  amenities: Amenity[]
  activities: Activity[]
  total_sessions: number
  listing_ratings: ListingRatings[]
  region: Region
  sessions: SessionWithUserPreloaded[]
}

export type CreateListing = {
  suggested_by: string
  listing_type: string
  region: string
  name: string
  company_name: string
  address_name: string
  description: string
  full_address: string
  longitude: number
  latitude: number
  contact_number: string
  country_dial_code: string
  keywords: string[]
  postal_code: string
  diaper_changing_mat_type: string
  opening_hours: string
  usage_durations: number[]
  usage_extension_durations: number[]
  max_number_of_usage_extensions: number
  number_of_private_feeding_rooms: number
  number_of_diaper_changing_mats: number
  main_image_file: File
  main_image_url: string
  sub_image_files: File[]
  sub_image_urls: string[]
  activities: string[]
  amenities: string[]
  note: string
  api_key: string
  pi_id: string
  pi_last_updated: DateTime
  lock_id: string
  lock_master_pin: string
  lock_bluetooth_admin_key: string
  firestore_id: string

  //Update Field
  main_image_id: string
  sub_image_ids: string[]
  is_hidden: boolean
  is_verified: boolean
  is_usage_extendable: boolean
  status: string
  door_is_lockable: boolean
  humidity: number
  temperature: number
}

export type UpdateListingActivityAmenity = {
  activities: {
    activity_slug: string
    is_hidden: boolean
  }[]
  amenities: {
    amenity_slug: string
    is_hidden: boolean
  }[]
}

export type UpdateListingImage = {
  image_file: File
  image_url: string
  is_appoved: boolean
  is_hidden: boolean
  is_main: boolean
  to_delete: boolean
}

export interface ListingResponse extends IDataResponse<Listing> {
  data: Array<Listing>
}
