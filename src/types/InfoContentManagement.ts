import { DateTime } from 'luxon'
import { IDataResponse } from '@/network/request'

export type InfoContentManagement = {
  id: number
  page_name: string
  slug: string
  content: string
  created_by: string
  updated_by: string
  created_at: DateTime
  updated_at: DateTime
}

export type CreateInfoContentManagement = {
  page_name: string
  content: string
}

export interface InfoContentManagementResponse extends IDataResponse<InfoContentManagement> {
  data: Array<InfoContentManagement>
}
