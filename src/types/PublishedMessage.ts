import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'
import { User } from './User'
import { UserDevice } from './UserDevice'

export type PublishedMessage = {
  id: number
  message_used: PublishedMessage
  publish_user: User
  receive_device: UserDevice
  created_at: DateTime
  updated_at: DateTime
}

export type CreatePublishedMessageInput = {
  users_selected?: string[]
  message_chosen: number
}

export interface PublishedMessageResponse extends IDataResponse<PublishedMessage> {
  data: Array<PublishedMessage>
}

export enum SendGroup {
  ALL_USERS = 'all_users',
  NEW_REGISTERED = 'new_registered',
  ACTIVE_30 = 'active_30'
}
