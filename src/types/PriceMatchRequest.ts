import { IDataResponse } from '@/network/request'
import { User } from './User'

export enum PriceMatchRequestStatus {
  APPROVE = 'approve',
  REJECT = 'reject',
  PENDING = 'pending'
}

export type PriceMatchRequest = {
  id: number
  customer_name: string
  customer_email: string
  order_number: string
  product_name: string
  product_brand: string
  product_model_number: string
  retailer: string
  price: number
  product_link: string
  comment: string
  status: PriceMatchRequestStatus
  user?: User
}

export type CreatePriceMatchRequest = {
  customer_name: string
  customer_email: string
  order_number?: string
  product_name: string
  product_brand: string
  product_model_number: string
  retailer: string
  price: number
  product_link: string
  comment?: string
}

export type UpdatePriceMatchRequest = {
  customer_name?: string
  customer_email?: string
  order_number?: string
  product_name?: string
  product_brand?: string
  product_model_number?: string
  retailer?: string
  price?: number
  product_link?: string
  comment?: string
  status?: PriceMatchRequestStatus
}

export interface PriceMatchRequestResponse extends IDataResponse<PriceMatchRequest> {
  data: Array<PriceMatchRequest>
}
