import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

export type ShopBanner = {
  id: number
  banner_name: string
  image_url: string
  action_link?: string
  created_at: DateTime
  updated_at: DateTime
}

export type CreateShopBanner = {
  banner_name: string
  image_file: File
  action_link?: string
}

export type UpdateShopBanner = {
  banner_name?: string
  image_file?: File
  action_link?: string
}

export interface ShopBannerResponse extends IDataResponse<ShopBanner> {
  data: Array<ShopBanner>
}
