import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

interface PreloadedDeviceSetting {
  id: string
  name: string
  created_by: string
  updated_by: string
  media_id: number
  created_at: DateTime
  updated_at: DateTime
}

export enum MediaType {
  image = 'image',
  video = 'video',
  text = 'text'
}

export type DigitalSignageMedia = {
  id: number
  media_url: string
  name: string
  type: MediaType
  created_by: string
  updated_by: string
  created_at: DateTime
  updated_at: DateTime
  device_settings: PreloadedDeviceSetting[]
}

export type CreateDigitalSignageMedia = {
  media_file: File
  name: string
  type: MediaType
}

export interface DigitalSignageMediaResponse extends IDataResponse<DigitalSignageMedia> {
  data: Array<DigitalSignageMedia>
}
