import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

export type ListingFlag = {
  id: string
  user_id: string
  listing_id: string
  category: string
  reason: string
  is_reviewed: boolean
  action: string
  action_reason: string
  action_by: string
  created_at: DateTime
  updated_at: DateTime
  reviewed_at: DateTime
  reference_images: string[]
}

export interface ListingFlagResponse extends IDataResponse<ListingFlag> {
  data: Array<ListingFlag>
}

export enum ListingFlagCategory {
  page_malfunction = 'Page Malfunction',
  duplicate_listing = 'Duplicate Listing',
  inaccurate_info = 'Inaccurate Info',
  privacy_concern = 'Privacy Concern',
  safety_hazard = 'Safety Hazard',
  other = 'other'
}

export enum ListingFlagAction {
  hide_listing = 'Hide Listing',
  no_action = 'No Action',
  other = 'other'
}

export type UpdateListingFlag = {
  user_id: string
  listing_id: string
  category?: ListingFlagCategory
  reason: string
  action: ListingFlagAction
  action_reason: string
}
