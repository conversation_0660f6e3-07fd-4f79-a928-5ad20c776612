import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

export type Amenity = {
  id: string
  name: string
  description: string
  font_icon_name: string
  is_hidden: boolean
  created_at: DateTime
  updated_at: DateTime
  slug: string
}

export type CreateAmenity = {
  name: string
  description: string
  font_icon_name: string
}

export interface AmenityResponse extends IDataResponse<Amenity> {
  data: Array<Amenity>
}
