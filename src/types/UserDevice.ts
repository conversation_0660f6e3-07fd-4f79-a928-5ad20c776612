import { IDataResponse } from '@/network/request'
import { DateTime } from 'luxon'

export type UserDevice = {
  id: number
  user_id: string
  device_token: string
  created_at: DateTime
  updated_at: DateTime
}

export type CreateUserDeviceInput = {
  user_id: string
  device_token: string
}

export type UpdateUserDeviceInput = {
  user_id?: string
  device_token?: string
}

export interface UserDeviceResponse extends IDataResponse<UserDevice> {
  data: Array<UserDevice>
}
