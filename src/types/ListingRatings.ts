import { DateTime } from 'luxon'
import { Session } from './Session'
import { User } from './User'
import { IDataResponse } from '@/network/request'

export type ListingRatings = {
  id: string
  listing_id: string
  session_id: string
  app_rating: number
  experience_rating: number
  listing_rating: number
  review: string
  username: string
  is_hidden: boolean
  session: Session
  user: User
  created_at: DateTime
  updated_at: DateTime
}

export interface ListingRatingsResponse extends IDataResponse<ListingRatings> {
  data: Array<ListingRatings>
}