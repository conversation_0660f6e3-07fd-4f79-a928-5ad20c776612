import {
  CreateDigitalSignageDeviceSetting,
  DigitalSignageDeviceSetting
} from '@/types/DigitalSignageDeviceSetting'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/ds/device-settings'
const getSingle = (id: string | number) => `/ds/device-settings/${id}`

const clientGetAll = () => {
  return client.get('/ds/device-settings')
}

const clientGetSingle = (id: string) => {
  return client.get(`/ds/device-settings/${id}`)
}

const clientCreate = (data: CreateDigitalSignageDeviceSetting) => {
  return client.post('/ds/device-settings', data)
}

const clientUpdate = (id: string | number, data: CreateDigitalSignageDeviceSetting) => {
  return client.put(`/ds/device-settings/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/ds/device-settings/${id}`)
}

const toRow = (
  data: IDataResponse<DigitalSignageDeviceSetting> | undefined
): DigitalSignageDeviceSetting[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<DigitalSignageDeviceSetting> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: DigitalSignageDeviceSetting): DigitalSignageDeviceSetting => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString()),
    media: {
      ...data.media,
      created_at: data.media.created_at && DateTime.fromISO(data.media.created_at.toString()),
      updated_at: data.media.updated_at && DateTime.fromISO(data.media.updated_at.toString())
    },
    devices: data.devices?.map(device => ({
      ...device,
      created_at: device.created_at && DateTime.fromISO(device.created_at.toString()),
      updated_at: device.updated_at && DateTime.fromISO(device.updated_at.toString())
    }))
  }
}

const DigitalSignageDeviceSettingService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default DigitalSignageDeviceSettingService
