import { Amenity, CreateAmenity } from '@/types/Amenity'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/admin/amenities'
const getSingle = (id: string) => `/admin/amenities/${id}`

const clientGetAll = () => {
  return client.get('/admin/amenities')
}

const clientGetSingle = (id: string) => {
  return client.get(`/admin/amenities/${id}`)
}

const clientGetSingleSlug = (slug: string) => {
  return client.get(`/admin/amenities/search-slug/${slug}`)
}

const clientCreate = (data: CreateAmenity) => {
  return client.post('/admin/amenities', data)
}

const clientUpdate = (id: string | number, data: CreateAmenity) => {
  return client.put(`/admin/amenities/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/admin/amenities/${id}`)
}

const toRow = (data: IDataResponse<Amenity> | undefined): Amenity[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Amenity> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: Amenity): Amenity => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const AmenityService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientGetSingleSlug,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default AmenityService
