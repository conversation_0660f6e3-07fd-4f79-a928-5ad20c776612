import client, { IDataResponse } from '../request'
import { ListingType } from '@/types/ListingType'
import { DateTime } from 'luxon'

const getAll = '/listing-types'
const getSingle = (id: string) => `listing-types/${id}`

const clientGetAll = () => {
  return client.get('/listing-types')
}

const clientGetSingle = (id: string) => {
  return client.get(`listing-types/${id}`)
}

const toRow = (data: IDataResponse<ListingType> | undefined): ListingType[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<ListingType> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: ListingType): ListingType => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const ListingTypeService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  toRow,
  toPaginate,
  serialize
}

export default ListingTypeService
