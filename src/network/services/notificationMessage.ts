import {
  CreateNotificationMessageInput,
  NotificationMessage,
  UpdateNotificationMessageInput
} from '@/types/NotificationMessage'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getNotificationMessages = '/admin/notification-messages'
const getNotificationMessage = (deviceId: string | number) =>
  `/admin/notification-messages/${deviceId}`

const clientGetAll = () => {
  return client.get('/admin/notification-messages')
}

const clientGetSingle = (deviceId: string | number) => {
  return client.get(`/admin/notification-messages/${deviceId}`)
}

const clientCreate = (data: CreateNotificationMessageInput) => {
  const formData = new FormData()
  formData.append('title', data.title)
  formData.append('message', data.message)
  data.image_url && formData.append('image_url', data.image_url)
  data.image_file && formData.append('image_file', data.image_file)

  return client.post('/admin/notification-messages', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const clientUpdate = (id: string | number, data: UpdateNotificationMessageInput) => {
  const formData = new FormData()
  data.title && formData.append('title', data.title)
  data.message && formData.append('message', data.message)
  data.image_url && formData.append('image_url', data.image_url)
  data.image_file && formData.append('image_file', data.image_file)

  return client.put(`/admin/notification-messages/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const clientDelete = (id: string | number) => {
  return client.delete(`/admin/notification-messages/${id}`)
}

const toRow = (data: IDataResponse<NotificationMessage> | undefined): NotificationMessage[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<NotificationMessage> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: NotificationMessage): NotificationMessage => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const NotificationMessageService = {
  getNotificationMessages,
  getNotificationMessage,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default NotificationMessageService
