import { CreateInfoContentManagement, InfoContentManagement } from '@/types/InfoContentManagement'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/cms/admin/info-contents'
const getSingle = (slug: string) => `/cms/admin/info-contents/${slug}`

const clientGetAll = () => {
  return client.get('/cms/admin/info-contents')
}

const clientGetSingle = (id: string) => {
  return client.get(`/cms/admin/info-contents/${id}`)
}

const clientCreate = (data: CreateInfoContentManagement) => {
  return client.post('/cms/admin/info-contents', data)
}
const clientUpdate = (id: string | number, data: CreateInfoContentManagement) => {
  return client.put(`/cms/admin/info-contents/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/cms/admin/info-contents/${id}`)
}

const toRow = (data: IDataResponse<InfoContentManagement> | undefined): InfoContentManagement[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<InfoContentManagement> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: InfoContentManagement) => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const InfoContentService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default InfoContentService
