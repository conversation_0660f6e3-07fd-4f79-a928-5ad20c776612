import { DateTime } from 'luxon'
import client, { IDataResponse } from '../request'
import { CreateShopBanner, ShopBanner, UpdateShopBanner } from '@/types/ShopBanner'

const getAll = '/cms/admin/shop-banners'
const getSingle = (id: string | number) => `/cms/admin/shop-banners/${id}`

const clientGetAll = () => {
  return client.get('/cms/admin/shop-banners')
}

const clientGetSingle = (id: string) => {
  return client.get(`/cms/admin/shop-banners/${id}`)
}

const clientCreate = (data: CreateShopBanner) => {
  const formData = new FormData()

  formData.append('banner_name', data.banner_name)
  data.action_link && formData.append('action_link', data.action_link)
  formData.append('image_file', data.image_file)

  return client.post('/cms/admin/shop-banners', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const clientUpdate = (id: string | number, data: UpdateShopBanner) => {
  const formData = new FormData()

  data.banner_name && formData.append('banner_name', data.banner_name)
  data.action_link && formData.append('action_link', data.action_link)
  data.image_file && formData.append('image_file', data.image_file)

  return client.put(`/cms/admin/shop-banners/${id}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const clientDelete = (id: string | number) => {
  return client.delete(`/cms/admin/shop-banners/${id}`)
}

const toRow = (data: IDataResponse<ShopBanner> | undefined): ShopBanner[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<ShopBanner> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: ShopBanner): ShopBanner => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const ShopBannerService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default ShopBannerService
