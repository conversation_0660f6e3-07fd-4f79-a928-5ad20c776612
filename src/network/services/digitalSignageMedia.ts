import { CreateDigitalSignageMedia, DigitalSignageMedia } from '@/types/DigitalSignageMedia'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/ds/media'
const getSingle = (id: string | number) => `/ds/media/${id}`

const clientGetAll = () => {
  return client.get('/ds/media')
}

const clientGetSingle = (id: string) => {
  return client.get(`/ds/media/${id}`)
}

const clientCreate = (data: CreateDigitalSignageMedia) => {
  return client.post('/ds/media', data)
}

const clientUpdate = (id: string | number, data: CreateDigitalSignageMedia) => {
  return client.put(`/ds/media/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/ds/media/${id}`)
}

const toRow = (data: IDataResponse<DigitalSignageMedia> | undefined): DigitalSignageMedia[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<DigitalSignageMedia> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: DigitalSignageMedia): DigitalSignageMedia => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString()),
    device_settings: data.device_settings?.map(setting => ({
      ...setting,
      created_at: setting.created_at && DateTime.fromISO(setting.created_at.toString()),
      updated_at: setting.updated_at && DateTime.fromISO(setting.updated_at.toString())
    }))
  }
}

const DigitalSignageMediaService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default DigitalSignageMediaService
