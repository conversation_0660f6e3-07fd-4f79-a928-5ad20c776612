import client from '../request' // , { IDataResponse }

export type RegisterState = {
  email: string
  password: string
  password_confirmation: string
  auth_code: string
}

export type LoginState = {
  email: string
  password: string
  login_account?: string
}

export type ResetPasswordState = {
  password: string
  new_password: string
  new_password_confirmation: string
}

export type ForgotPassword = {
  email: string
  password: string
  password_confirmation: string
  auth_code: string
}

export type User = {
  id: number
  username: string
  email: string
  role?: string
}

/// swr
const findMyself = '/admin/me'
/// swr

/// axios
const clientFindMyself = () => {
  return client.get('/admin/me')
}

const login = (data: LoginState) => {
  return client.post('/admin/login', data)
}

const logout = () => {
  return client.post('/admin/me/logout')
}

const AuthService = {
  login,
  logout,
  findMyself,
  clientFindMyself
}

export default AuthService
