import client, { IDataResponse } from '../request'
import { Region } from '@/types/Region'
import { DateTime } from 'luxon'

const getAll = '/admin/regions'
const getSingle = (id: string) => `/admin/regions/${id}`

const clientGetAll = () => {
  return client.get('/admin/regions')
}

const clientGetSingle = (id: string) => {
  return client.get(`/admin/regions/${id}`)
}

const clientGetSingleSlug = (slug: string) => {
  return client.get(`/admin/regions/search-slug/${slug}`)
}

const toRow = (data: IDataResponse<Region> | undefined): Region[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Region> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: Region): Region => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const RegionService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientGetSingleSlug,
  toRow,
  toPaginate,
  serialize
}

export default RegionService
