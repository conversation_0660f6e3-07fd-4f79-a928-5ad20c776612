import { Activity, CreateActivity } from '@/types/Activity'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/admin/activities'
const getSingle = (id: string) => `/admin/activities/${id}`

const clientGetAll = () => {
  return client.get('/admin/activities')
}

const clientGetSingle = (id: string) => {
  return client.get(`/admin/activities/${id}`)
}

const clientGetSingleSlug = (slug: string) => {
  return client.get(`/admin/activities/search-slug/${slug}`)
}

const clientCreate = (data: CreateActivity) => {
  return client.post('/admin/activities', data)
}

const clientUpdate = (id: string | number, data: CreateActivity) => {
  return client.put(`/admin/activities/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/admin/activities/${id}`)
}

const toRow = (data: IDataResponse<Activity> | undefined): Activity[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Activity> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: Activity): Activity => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const ActivityService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientGetSingleSlug,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default ActivityService
