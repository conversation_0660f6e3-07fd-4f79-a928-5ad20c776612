import { ListingFile } from '@/types/ListingFile'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/listing-files'
const getSingle = (id: string) => `listing-files/${id}`

const clientGetAll = () => {
  return client.get('/listing-files')
}

const clientGetSingle = (id: string) => {
  return client.get(`listing-files/${id}`)
}

const toRow = (data: IDataResponse<ListingFile> | undefined): ListingFile[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<ListingFile> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: ListingFile): ListingFile => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const ListingFileService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  toRow,
  toPaginate,
  serialize
}

export default ListingFileService
