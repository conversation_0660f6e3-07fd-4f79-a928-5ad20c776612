import { CreateDigitalSignageDevice, DigitalSignageDevice } from '@/types/DigitalSignageDevice'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/ds/devices'
const getSingle = (id: string | number) => `/ds/devices/${id}`

const clientGetAll = () => {
  return client.get('/ds/devices')
}

const clientGetSingle = (id: string) => {
  return client.get(`/ds/devices/${id}`)
}

const clientCreate = (data: CreateDigitalSignageDevice) => {
  return client.post('/ds/devices', data)
}

const clientUpdate = (id: string | number, data: CreateDigitalSignageDevice) => {
  return client.put(`/ds/devices/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/ds/devices/${id}`)
}

const toRow = (data: IDataResponse<DigitalSignageDevice> | undefined): DigitalSignageDevice[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<DigitalSignageDevice> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: DigitalSignageDevice): DigitalSignageDevice => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString()),
    listing: data.listing ? {
      ...data.listing,
      created_at: data.listing.created_at && DateTime.fromISO(data.listing.created_at.toString()),
      updated_at: data.listing.updated_at && DateTime.fromISO(data.listing.updated_at.toString())
    } : undefined,
    device_setting: data.device_setting ? {
      ...data.device_setting,
      created_at: data.device_setting.created_at && DateTime.fromISO(data.device_setting.created_at.toString()),
      updated_at: data.device_setting.updated_at && DateTime.fromISO(data.device_setting.updated_at.toString())
    } : undefined
  }
}

const DigitalSignageDeviceService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default DigitalSignageDeviceService
