import { CreateEventInfo, EventInfo, UpdateEventInfo } from '@/types/EventInfo'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/cms/admin/event-infos'
const getSingle = (id: string | number) => `/cms/admin/event-infos/${id}`

const clientGetAll = () => {
  return client.get('/cms/admin/event-infos')
}

const clientGetSingle = (id: string) => {
  return client.get(`/cms/admin/event-infos/${id}`)
}

const clientCreate = (data: CreateEventInfo) => {
  const formData = new FormData()

  formData.append('image_file', data.image_file)
  formData.append('title', data.title)
  formData.append('description', data.description)
  formData.append('content', data.content)
  formData.append('category_tag', data.category_tag)

  return client.post('/cms/admin/event-infos', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const clientUpdate = (id: string | number, data: UpdateEventInfo) => {
  const formData = new FormData()

  data.image_file && formData.append('image_file', data.image_file)
  data.title && formData.append('title', data.title)
  data.description && formData.append('description', data.description)
  data.content && formData.append('content', data.content)
  data.category_tag && formData.append('category_tag', data.category_tag)

  return client.put(`/cms/admin/event-infos/${id}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

const clientDelete = (id: string | number) => {
  return client.delete(`/cms/admin/event-infos/${id}`)
}

const toRow = (data: IDataResponse<EventInfo> | undefined): EventInfo[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<EventInfo> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: EventInfo): EventInfo => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const EventInfoService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default EventInfoService
