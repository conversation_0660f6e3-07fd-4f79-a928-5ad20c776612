import { DigitalSignageListing } from '@/types/DigitalSignageListing'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/ds/listings'
const getSingle = (id: string | number) => `/ds/listings/${id}`

const clientGetAll = () => {
  return client.get('/ds/listings')
}

const clientGetSingle = (id: string) => {
  return client.get(`/ds/listings/${id}`)
}

const toRow = (data: IDataResponse<DigitalSignageListing> | undefined): DigitalSignageListing[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<DigitalSignageListing> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: DigitalSignageListing): DigitalSignageListing => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const DigitalSignageListingService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  toRow,
  toPaginate,
  serialize
}

export default DigitalSignageListingService
