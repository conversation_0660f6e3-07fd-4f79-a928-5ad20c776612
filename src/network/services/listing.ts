import {
  Listing,
  CreateListing,
  UpdateListingImage,
  UpdateListingActivityAmenity
} from '@/types/Listing'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/admin/listings'
const getSingle = (id: string) => `/admin/listings/${id}`

const clientGetAll = () => {
  return client.get('/admin/listings')
}

const clientCreate = (data: CreateListing) => {
  return client.post('/admin/listings', data)
}

const clientUpdate = (id: string | number, data: Partial<CreateListing>) => {
  return client.put(`/admin/listings/${id}`, data)
}

const clientUpdateActivitiesAmenities = (
  id: string | number,
  data: Partial<UpdateListingActivityAmenity>
) => {
  return client.put(`/admin/listings/${id}`, data)
}

const clientUpdateImage = (id: string | number, data: Partial<UpdateListingImage>) => {
  return client.put(`/admin/listings/update-image/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/admin/listings/${id}`)
}

const toRow = (data: IDataResponse<Listing> | undefined): Listing[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Listing> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: Listing): Listing => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const ListingService = {
  getAll,
  getSingle,
  clientGetAll,
  clientCreate,
  clientUpdate,
  clientUpdateActivitiesAmenities,
  clientUpdateImage,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default ListingService
