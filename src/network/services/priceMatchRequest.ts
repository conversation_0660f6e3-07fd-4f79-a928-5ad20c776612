import client, { IDataResponse } from '../request'
import {
  CreatePriceMatchRequest,
  PriceMatchRequest,
  UpdatePriceMatchRequest
} from '@/types/PriceMatchRequest'

const getAll = '/cms/admin/price-match-requests'
const getSingle = (id: string) => `/cms/admin/price-match-requests/${id}`

const clientGetAll = () => {
  return client.get('/cms/admin/price-match-requests')
}

const clientGetSingle = (id: string) => {
  return client.get(`/cms/admin/price-match-requests/${id}`)
}

const clientCreate = (data: CreatePriceMatchRequest) => {
  return client.post('/cms/admin/price-match-requests', data)
}

const clientUpdate = (id: string | number, data: UpdatePriceMatchRequest) => {
  return client.put(`/cms/admin/price-match-requests/${id}`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/cms/admin/price-match-requests/${id}`)
}

const toRow = (data: IDataResponse<PriceMatchRequest> | undefined): PriceMatchRequest[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...element,
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<PriceMatchRequest> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const PriceMatchRequestService = {
  getAll,
  getSingle,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientDelete,
  toRow,
  toPaginate
}

export default PriceMatchRequestService
