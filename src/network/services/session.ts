import { Session, UpdateSession } from '@/types/Session'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/admin/sessions'
const getSingle = (id: string) => `/admin/sessions/${id}`

const clientGetAll = () => {
  return client.get('/admin/sessions')
}

const clientUpdate = (id: string | number, data: UpdateSession) => {
  return client.put(`/admin/sessions/${id}`, data)
}

const clientUpdateHide = (id: string | number, data: UpdateSession) => {
  return client.put(`/admin/sessions/${id}/hide-unhide-session`, data)
}

const clientDelete = (id: string | number) => {
  return client.delete(`/admin/sessions/${id}`)
}

const toRow = (data: IDataResponse<Session> | undefined): Session[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<Session> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: Session): Session => {
  return {
    ...data,
    started_at: data.started_at && DateTime.fromISO(data.started_at.toString()),
    expected_ended_at: data.expected_ended_at && DateTime.fromISO(data.expected_ended_at.toString()),
    actual_ended_at: data.actual_ended_at ? DateTime.fromISO(data.actual_ended_at.toString()) : undefined,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const SessionService = {
  getAll,
  getSingle,
  clientGetAll,
  clientUpdate,
  clientUpdateHide,
  clientDelete,
  toRow,
  toPaginate,
  serialize
}

export default SessionService
