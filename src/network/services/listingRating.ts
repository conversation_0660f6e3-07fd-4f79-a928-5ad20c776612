import { ListingRatings } from '@/types/ListingRatings'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/admin/listing-ratings'
const getByListingId = (listingId: string) => `/admin/listing-ratings?listing_id=${listingId}`
const getSingle = (id: string) => `/admin/listing-ratings/${id}`

const clientGetAll = () => {
  return client.get('/admin/listing-ratings')
}

const clientGetSingle = (id: string) => {
  return client.get(`/admin/listing-ratings/${id}`)
}

const clientGetByListingId = (listingId: string) => {
  return client.get(`/admin/listing-ratings?listing_id=${listingId}`)
}

const toRow = (data: IDataResponse<ListingRatings> | undefined): ListingRatings[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<ListingRatings> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: ListingRatings): ListingRatings => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const ListingRatingService = {
  getAll,
  getSingle,
  getByListingId,
  clientGetAll,
  clientGetSingle,
  clientGetByListingId,
  toRow,
  toPaginate,
  serialize
}

export default ListingRatingService
