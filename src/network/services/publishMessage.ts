import { CreatePublishedMessageInput, PublishedMessage, SendGroup } from '@/types/PublishedMessage'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getPublishedMessages = '/admin/publish-messages'
const getPublishedMessage = (publishedMessageId: string | number) =>
  `/admin/publish-messages/${publishedMessageId}`

const clientGetAll = () => {
  return client.get('/admin/publish-messages')
}

const clientGetSingle = (publishedMessageId: string | number) => {
  return client.get(`/admin/publish-messages/${publishedMessageId}`)
}

const clientCreate = (data: CreatePublishedMessageInput, send_all: SendGroup[]) => {
  return client.post(`/admin/publish-messages?group=${send_all.join(',')}`, data)
}

const toRow = (data: IDataResponse<PublishedMessage> | undefined): PublishedMessage[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<PublishedMessage> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: PublishedMessage): PublishedMessage => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const PublishedMessageService = {
  getPublishedMessages,
  getPublishedMessage,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  toRow,
  toPaginate,
  serialize
}

export default PublishedMessageService
