import { ListingFlag, UpdateListingFlag } from '@/types/ListingFlag'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getAll = '/admin/listing-flags'
const getSingle = (id: string) => `/admin/listing-flags/${id}`

const clientGetAll = () => {
  return client.get('/admin/listing-flags')
}

const clientDelete = (id: string | number) => {
  return client.delete(`/admin/listing-flags/${id}`)
}

const clientUpdate = (id: string | number, data: UpdateListingFlag) => {
  return client.put(`/admin/listing-flags/${id}`, {
    action: data.action,
    action_reason: data.action_reason,
    category: data.category
  })
}

const toRow = (data: IDataResponse<ListingFlag> | undefined): ListingFlag[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<ListingFlag> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: ListingFlag) => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString()),
    reviewed_at: data.reviewed_at && DateTime.fromISO(data.reviewed_at.toString())
  }
}

const ListingFlagService = {
  getAll,
  getSingle,
  clientGetAll,
  clientDelete,
  clientUpdate,
  toRow,
  toPaginate,
  serialize
}

export default ListingFlagService
