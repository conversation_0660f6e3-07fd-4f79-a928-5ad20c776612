import { CreateUserInput, User } from '@/types/User'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getUsers = '/admin/users'
const getUser = (userId: string | number) => `/admin/users/${userId}`

const clientGetAll = () => {
  return client.get('/admin/users')
}

const clientGetSingle = (userId: string | number) => {
  return client.get(`/admin/users/${userId}`)
}

const clientCreate = (data: CreateUserInput) => {
  return client.post('/admin/users', data)
}

const clientUpdate = (id: string | number, data: CreateUserInput) => {
  return client.put(`/admin/users/${id}`, data)
}

const clientResetSelfieFailCount = (id: string | number) => {
  return client.post(`/admin/users/reset-selfie-fail-count/${id}`)
}

const toRow = (data: IDataResponse<User> | undefined): User[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<User> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: User): User => {
  return {
    ...data,
    birthday: data.birthday && DateTime.fromISO(data.birthday.toString()),
    children_birthday: data.children_birthday?.map((d) => d && DateTime.fromISO(d.toString())),
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const UserService = {
  getUsers,
  getUser,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  clientResetSelfieFailCount,
  toRow,
  toPaginate,
  serialize
}

export default UserService
