import { CreateUserDeviceInput, UpdateUserDeviceInput, UserDevice } from '@/types/UserDevice'
import client, { IDataResponse } from '../request'
import { DateTime } from 'luxon'

const getDevices = '/admin/devices'
const getDevice = (deviceId: string | number) => `/admin/devices/${deviceId}`

const clientGetAll = () => {
  return client.get('/admin/devices')
}

const clientGetSingle = (deviceId: string | number) => {
  return client.get(`/admin/devices/${deviceId}`)
}

const clientCreate = (data: CreateUserDeviceInput) => {
  return client.post('/admin/devices', data)
}

const clientUpdate = (id: string | number, data: UpdateUserDeviceInput) => {
  return client.put(`/admin/devices/${id}`, data)
}

const toRow = (data: IDataResponse<UserDevice> | undefined): UserDevice[] => {
  if (data?.data && data?.data?.length > 0) {
    return data?.data?.map((element) => {
      return {
        ...serialize(element),
        key: element.id
      }
    })
  }

  return []
}

const toPaginate = (data: IDataResponse<UserDevice> | undefined) => {
  return {
    total: data?.meta?.total ?? 0,
    lastPage: data?.meta?.last_page ?? 0
  }
}

const serialize = (data: UserDevice): UserDevice => {
  return {
    ...data,
    created_at: data.created_at && DateTime.fromISO(data.created_at.toString()),
    updated_at: data.updated_at && DateTime.fromISO(data.updated_at.toString())
  }
}

const UserDeviceService = {
  getDevices,
  getDevice,
  clientGetAll,
  clientGetSingle,
  clientCreate,
  clientUpdate,
  toRow,
  toPaginate,
  serialize
}

export default UserDeviceService
