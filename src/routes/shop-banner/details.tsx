import FormSheet from '@/components/Form/FormSheet'
import ShopBannerForm from '@/components/ShopBanner/ShopBannerForm'
import ShopBannerCard from '@/components/ShopBanner/ShopBannerCard'
import { useShopBanner } from '@/hooks/useShopBanner'

import { FC } from 'react'
import { useParams } from 'react-router-dom'
import DeleteButton from '@/components/Button/DeleteButton'
import ShopBannerService from '@/network/services/shopBanner'

const ShopBannerDetails: FC = () => {
  const { id } = useParams()

  if (!id) {
    return <></>
  }

  return <View {...{ id }} />
}

const View: FC<{ id: string }> = ({ id }) => {
  const { shopBanner, isLoading } = useShopBanner(id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet
                title="Edit Shop Banner"
                button="Edit Shop Banner"
                edit
                formId="shop-banner"
              >
                <ShopBannerForm shopBanner={shopBanner} />
              </FormSheet>
              <DeleteButton
                name="Shop Banner"
                id={shopBanner.id}
                deleteService={ShopBannerService.clientDelete}
                deleteSuccessRoute={'/shop-banners'}
              />
            </div>

            <ShopBannerCard shopBanner={shopBanner} />
          </>
        )}
      </div>
    </div>
  )
}

export default ShopBannerDetails
