import FormSheet from '@/components/Form/FormSheet'
import { FC } from 'react'
import { useParams } from 'react-router-dom'
import DeleteButton from '@/components/Button/DeleteButton'
import DigitalSignageMediaService from '@/network/services/digitalSignageMedia'
import DigitalSignageMediaForm from '@/components/DigitalSignageMedia/DigitalSignageMediaForm'
import DigitalSignageMediaCard from '@/components/DigitalSignageMedia/DigitalSignageMediaCard'
import { useDigitalSignageMedia } from '@/hooks/useDigitalSignageMedia'

const DigitalSignageMediaDetails: FC = () => {
  const { id } = useParams()

  if (!id) {
    return <></>
  }

  return <View {...{ id }} />
}

const View: FC<{ id: string }> = ({ id }) => {
  const { digitalSignageMedia, isLoading } = useDigitalSignageMedia(id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet
                title="Edit Digital Signage Media"
                button="Edit Digital Signage Media"
                edit
                formId="digital-signage-media"
              >
                <DigitalSignageMediaForm media={digitalSignageMedia} />
              </FormSheet>
              <DeleteButton
                name="Digital Signage Media"
                id={digitalSignageMedia.id}
                deleteService={DigitalSignageMediaService.clientDelete}
                deleteSuccessRoute={'/digital-signage-medias'}
              />
            </div>

            <DigitalSignageMediaCard media={digitalSignageMedia} />
          </>
        )}
      </div>
    </div>
  )
}

export default DigitalSignageMediaDetails
