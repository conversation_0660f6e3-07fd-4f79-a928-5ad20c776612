import AmenityCard from '@/components/Amenity/AmenityCard'
import AmenityForm from '@/components/Amenity/AmenityForm'
import DeleteButton from '@/components/Button/DeleteButton'
import FormSheet from '@/components/Form/FormSheet'
import { useAmenity } from '@/hooks/useAmenity'
import AmenityService from '@/network/services/amenity'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const AmenityDetails: FC = () => {
  const { amenity_id } = useParams()

  if (!amenity_id) {
    return <></>
  }

  return <View {...{ amenity_id }} />
}

const View: FC<{ amenity_id: string }> = ({ amenity_id }) => {
  const { amenity, isLoading } = useAmenity(amenity_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet title="Edit Amenity" button="Edit Amenity" edit formId="amenity-form">
                <AmenityForm amenity={amenity} />
              </FormSheet>

              <DeleteButton
                name="Amenity"
                id={amenity_id}
                deleteService={AmenityService.clientDelete}
                deleteSuccessRoute={'/amenities'}
              />
            </div>
            <div className="">
              <div>
                <AmenityCard amenity={amenity} />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default AmenityDetails
