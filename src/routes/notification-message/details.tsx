import NotificationMessageCard from '@/components/NotificationMessage/NotificationMessageCard'
import { useNotificationMessage } from '@/hooks/useNotificationMessage'
import { FC } from 'react'
import { useParams } from 'react-router-dom'
import FormSheet from '@/components/Form/FormSheet'
import NotificationMessageForm from '@/components/NotificationMessage/NotificationMessageForm'
import DeleteButton from '@/components/Button/DeleteButton'
import NotificationMessageService from '@/network/services/notificationMessage'
import NotificationMessagePublishCard from '@/components/NotificationMessage/NotificationMessagePublishCard'

const NotificationMessageDetails: FC = () => {
  const { id } = useParams()

  if (!id) {
    return <></>
  }

  return <View {...{ id }} />
}

const View: FC<{ id: string }> = ({ id }) => {
  const { notificationMessage, isLoading } = useNotificationMessage(id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet
                title="Edit Notification Message"
                button="Edit Notification Message"
                edit
                formId="notification-message"
              >
                <NotificationMessageForm notificationMessage={notificationMessage} />
              </FormSheet>
              <DeleteButton
                name="Notification Message"
                id={notificationMessage.id}
                deleteService={NotificationMessageService.clientDelete}
                deleteSuccessRoute={'/notification-messages'}
              />
            </div>
            <NotificationMessageCard notificationMessage={notificationMessage} />
            <NotificationMessagePublishCard message={notificationMessage} />
          </>
        )}
      </div>
    </div>
  )
}

export default NotificationMessageDetails
