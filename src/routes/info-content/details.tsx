import DeleteButton from '@/components/Button/DeleteButton'
import FormSheet from '@/components/Form/FormSheet'
import InfoContentCard from '@/components/InfoContent/InfoContentCard'
import InfoContentForm from '@/components/InfoContent/InfoContentForm'
import { useInfoContent } from '@/hooks/useInfoContent'
import InfoContentService from '@/network/services/infoContent'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const InfoContentDetails: FC = () => {
  const { info_slug } = useParams()

  if (!info_slug) {
    return <></>
  }

  return <View {...{ info_slug }} />
}

const View: FC<{ info_slug: string }> = ({ info_slug }) => {
  const { infoContent, isLoading } = useInfoContent(info_slug)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet
                title="Edit Info Content"
                button="Edit Info Content"
                edit
                formId="create-info"
              >
                <InfoContentForm infoContent={infoContent} />
              </FormSheet>

              <DeleteButton
                name="Info Content"
                id={infoContent.slug}
                deleteService={InfoContentService.clientDelete}
                deleteSuccessRoute={'/pages'}
              />
            </div>
            <InfoContentCard infoContent={infoContent} />
          </>
        )}
      </div>
    </div>
  )
}

export default InfoContentDetails
