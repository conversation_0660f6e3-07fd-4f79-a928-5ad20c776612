import PriceMatchRequestCard from '@/components/PriceMatchRequest/PriceMatchRequestCard'
import ReviewButton from '@/components/PriceMatchRequest/ReviewButton'
import { usePriceMatchRequest } from '@/hooks/usePriceMatchRequest'
import PriceMatchRequestService from '@/network/services/priceMatchRequest'
import { PriceMatchRequestStatus } from '@/types/PriceMatchRequest'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const PriceMatchRequestDetails: FC = () => {
  const { id } = useParams()

  if (!id) {
    return <></>
  }

  return <View {...{ id }} />
}

const View: FC<{ id: string }> = ({ id }) => {
  const { priceMatchRequest, isLoading } = usePriceMatchRequest(id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              {priceMatchRequest.status != PriceMatchRequestStatus.APPROVE && (
                <ReviewButton
                  name="Price Match Request"
                  id={priceMatchRequest?.id}
                  reviewService={PriceMatchRequestService.clientUpdate}
                  reviewSuccessRoute={'/price-match-requests'}
                  priceMatchRequestStatus={PriceMatchRequestStatus.APPROVE}
                />
              )}
              {priceMatchRequest.status != PriceMatchRequestStatus.REJECT && (
                <ReviewButton
                  name="Price Match Request"
                  id={priceMatchRequest?.id}
                  reviewService={PriceMatchRequestService.clientUpdate}
                  reviewSuccessRoute={'/price-match-requests'}
                  priceMatchRequestStatus={PriceMatchRequestStatus.REJECT}
                />
              )}
              {priceMatchRequest.status != PriceMatchRequestStatus.PENDING && (
                <ReviewButton
                  name="Price Match Request"
                  id={priceMatchRequest?.id}
                  reviewService={PriceMatchRequestService.clientUpdate}
                  reviewSuccessRoute={'/price-match-requests'}
                  priceMatchRequestStatus={PriceMatchRequestStatus.PENDING}
                />
              )}
            </div>
            <PriceMatchRequestCard priceMatchRequest={priceMatchRequest} />
          </>
        )}
      </div>
    </div>
  )
}

export default PriceMatchRequestDetails
