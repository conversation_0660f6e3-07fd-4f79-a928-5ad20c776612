import DeleteButton from '@/components/Button/DeleteButton'
import SessionCard from '@/components/Session/SessionCard'
import { useSession } from '@/hooks/useSession'
import SessionService from '@/network/services/session'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const SessionDetails: FC = () => {
  const { session_id } = useParams()

  if (!session_id) {
    return <></>
  }

  return <View {...{ session_id }} />
}

const View: FC<{ session_id: string }> = ({ session_id }) => {
  const { session, isLoading } = useSession(session_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end">
              <DeleteButton
                name="Session"
                id={session_id}
                deleteService={SessionService.clientDelete}
                deleteSuccessRoute={'/sessions'}
              />
            </div>
            <SessionCard session={session} />
          </>
        )}
      </div>
    </div>
  )
}

export default SessionDetails
