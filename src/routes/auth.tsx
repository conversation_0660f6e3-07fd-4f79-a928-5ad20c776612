import { UserAuthForm } from '@/components/Auth/UserAuthForm'
import useAuth from '@/hooks/useAuth'
import { Navigate } from 'react-router-dom'
import { useDocumentTitle } from '@/hooks/useDocumentTitle'
// import { Image } from '@unpic/react'

const AuthPage = () => {
  const { user } = useAuth()

  // Set the document title for the login page
  useDocumentTitle('Login', true)

  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  return (
    <>
      <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-background p-6 md:p-10">
        <div className="w-full max-w-sm">
          <UserAuthForm />
        </div>
      </div>
    </>
  )
}

export default AuthPage
