import ActivityCard from '@/components/Activity/ActivityCard'
import ActivityForm from '@/components/Activity/ActivityForm'
import ImageCard from '@/components/Common/ImageCard'
import DeleteButton from '@/components/Button/DeleteButton'
import FormSheet from '@/components/Form/FormSheet'
import { useActivity } from '@/hooks/useActivity'
import ActivityService from '@/network/services/activity'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const ActivityDetails: FC = () => {
  const { activity_id } = useParams()

  if (!activity_id) {
    return <></>
  }

  return <View {...{ activity_id }} />
}

const View: FC<{ activity_id: string }> = ({ activity_id }) => {
  const { activity, isLoading } = useActivity(activity_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet title="Edit Activity" button="Edit Activity" edit formId="activity-form">
                <ActivityForm activity={activity} />
              </FormSheet>

              <DeleteButton
                name="Activity"
                id={activity_id}
                deleteService={ActivityService.clientDelete}
                deleteSuccessRoute={'/activities'}
              />
            </div>
            <div className="grid md:grid-cols-[4fr_2fr] gap-4">
              <div>
                <ActivityCard activity={activity} />
              </div>

              <div>
                <ImageCard image_url={activity.image_url} />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ActivityDetails
