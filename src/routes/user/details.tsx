import UserCard from '@/components/User/UserCard'
import { useUser } from '@/hooks/useUser'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const UserDetails: FC = () => {
  const { user_id } = useParams()

  if (!user_id) {
    return <></>
  }

  return <View {...{ user_id }} />
}

const View: FC<{ user_id: string | number }> = ({ user_id }) => {
  const { user, isLoading } = useUser(user_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && <UserCard user={user} />}
      </div>
    </div>
  )
}

export default UserDetails
