import FormSheet from '@/components/Form/FormSheet'
import { FC } from 'react'
import { useParams } from 'react-router-dom'
import DeleteButton from '@/components/Button/DeleteButton'
import DigitalSignageDeviceSettingForm from '@/components/DigitalSignageDeviceSetting/DigitalSignageDeviceSettingForm'
import DigitalSignageDeviceSettingCard from '@/components/DigitalSignageDeviceSetting/DigitalSignageDeviceSettingCard'
import { useDigitalSignageDeviceSetting } from '@/hooks/useDigitalSignageDeviceSetting'
import DigitalSignageDeviceSettingService from '@/network/services/digitalSignageDeviceSetting'

const DigitalSignageDeviceSettingDetails: FC = () => {
  const { id } = useParams()

  if (!id) {
    return <></>
  }

  return <View {...{ id }} />
}

const View: FC<{ id: string }> = ({ id }) => {
  const { digitalSignageDeviceSetting, isLoading } = useDigitalSignageDeviceSetting(id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet
                title="Edit Digital Signage Device Setting"
                button="Edit Digital Signage Device Setting"
                edit
                formId="digital-signage-device-setting"
              >
                <DigitalSignageDeviceSettingForm deviceSetting={digitalSignageDeviceSetting} />
              </FormSheet>
              <DeleteButton
                name="Digital Signage Device Setting"
                id={digitalSignageDeviceSetting.id}
                deleteService={DigitalSignageDeviceSettingService.clientDelete}
                deleteSuccessRoute={'/digital-signage-device-settings'}
              />
            </div>

            <DigitalSignageDeviceSettingCard deviceSetting={digitalSignageDeviceSetting} />
          </>
        )}
      </div>
    </div>
  )
}

export default DigitalSignageDeviceSettingDetails
