import FormSheet from '@/components/Form/FormSheet'
import { FC } from 'react'
import { useParams } from 'react-router-dom'
import DeleteButton from '@/components/Button/DeleteButton'
import DigitalSignageDeviceForm from '@/components/DigitalSignageDevice/DigitalSignageDeviceForm'
import DigitalSignageDeviceCard from '@/components/DigitalSignageDevice/DigitalSignageDeviceCard'
import { useDigitalSignageDevice } from '@/hooks/useDigitalSignageDevice'
import DigitalSignageDeviceService from '@/network/services/digitalSignageDevice'

const DigitalSignageDeviceDetails: FC = () => {
  const { id } = useParams()

  if (!id) {
    return <></>
  }

  return <View {...{ id }} />
}

const View: FC<{ id: string }> = ({ id }) => {
  const { digitalSignageDevice, isLoading } = useDigitalSignageDevice(id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet
                title="Edit Digital Signage Device"
                button="Edit Digital Signage Device"
                edit
                formId="digital-signage-device"
              >
                <DigitalSignageDeviceForm device={digitalSignageDevice} />
              </FormSheet>
              <DeleteButton
                name="Digital Signage Device"
                id={digitalSignageDevice.id}
                deleteService={DigitalSignageDeviceService.clientDelete}
                deleteSuccessRoute={'/digital-signage-devices'}
              />
            </div>

            <DigitalSignageDeviceCard device={digitalSignageDevice} />
          </>
        )}
      </div>
    </div>
  )
}

export default DigitalSignageDeviceDetails
