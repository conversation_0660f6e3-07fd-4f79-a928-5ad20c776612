import FormSheet from '@/components/Form/FormSheet'
import { FC } from 'react'
import { useParams } from 'react-router-dom'
import DeleteButton from '@/components/Button/DeleteButton'
import { useEventInfo } from '@/hooks/useEventInfo'
import EventInfoForm from '@/components/EventInfo/EventInfoForm'
import EventInfoService from '@/network/services/eventInfo'
import EventInfoCard from '@/components/EventInfo/EventInfoCard'

const EventInfoDetails: FC = () => {
  const { id } = useParams()

  if (!id) {
    return <></>
  }

  return <View {...{ id }} />
}

const View: FC<{ id: string }> = ({ id }) => {
  const { eventInfo, isLoading } = useEventInfo(id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <FormSheet
                title="Edit Announcement"
                button="Edit Announcement"
                edit
                formId="event-info"
              >
                <EventInfoForm eventInfo={eventInfo} />
              </FormSheet>
              <DeleteButton
                name="Announcement"
                id={eventInfo.id}
                deleteService={EventInfoService.clientDelete}
                deleteSuccessRoute={'/announcements'}
              />
            </div>

            <EventInfoCard eventInfo={eventInfo} />
          </>
        )}
      </div>
    </div>
  )
}

export default EventInfoDetails
