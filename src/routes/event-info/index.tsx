import EventInfoForm from '@/components/EventInfo/EventInfoForm'
import EventInfoTable from '@/components/EventInfo/EventInfoTable'
import FormSheet from '@/components/Form/FormSheet'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { EventCategoryTag } from '@/types/EventInfo'

const EventInfoPage = () => {
  return (
    <>
      <div className="flex-1 space-y-4 p-4 md:p-8 md:pt-0">
        <div className="flex items-center justify-end space-y-2">
          <FormSheet title="New Announcement" button="Create New Announcement" formId="event-info">
            <EventInfoForm />
          </FormSheet>
        </div>
        <Tabs
          defaultValue="new_pods"
          className="space-y-4"
          enableUrlHash
          tabValues={["new_pods", "latest_news", "events"]}
        >
          <TabsList>
            <TabsTrigger value="new_pods">New Pods</TabsTrigger>
            <TabsTrigger value="latest_news">Latest News</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
          </TabsList>
          <TabsContent value="new_pods">
            <EventInfoTable categoryTag={EventCategoryTag.newPods} />
          </TabsContent>
          <TabsContent value="latest_news">
            <EventInfoTable categoryTag={EventCategoryTag.latestNews} />
          </TabsContent>
          <TabsContent value="events">
            <EventInfoTable categoryTag={EventCategoryTag.events} />
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}

export default EventInfoPage
