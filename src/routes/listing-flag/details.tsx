import DeleteButton from '@/components/Button/DeleteButton'
import FormSheet from '@/components/Form/FormSheet'
import ListingFlagCard from '@/components/ListingFlag/ListingFlagCard'
import ListingFlagForm from '@/components/ListingFlag/ListingFlagForm'
import { useListingFlag } from '@/hooks/useListingFlag'
import ListingFlagService from '@/network/services/listingFlag'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const ListingFlagDetails: FC = () => {
  const { listing_flag_id } = useParams()

  if (!listing_flag_id) {
    return <></>
  }

  return <View {...{ listing_flag_id }} />
}

const View: FC<{ listing_flag_id: string }> = ({ listing_flag_id }) => {
  const { listingFlag, isLoading } = useListingFlag(listing_flag_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-3">
              <DeleteButton
                name="Listing Flag"
                id={listing_flag_id}
                deleteService={ListingFlagService.clientDelete}
                deleteSuccessRoute={'/listing-flags'}
              />

              <FormSheet
                title="Review Flag Form"
                button="Review Listing Flag"
                formId="review-listing-flag-form"
                disable={listingFlag.reviewed_at ? true : false}
              >
                <ListingFlagForm listingFlag={listingFlag} />
              </FormSheet>
            </div>
            <div className="grid grid-cols-[6fr] gap-4">
              <div>
                <ListingFlagCard listingFlag={listingFlag} />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ListingFlagDetails
