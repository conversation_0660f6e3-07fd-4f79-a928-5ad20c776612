import DeleteButton from '@/components/Button/DeleteButton'
import ListingCard from '@/components/Listing/ListingCard'
import { useListing } from '@/hooks/useListing'
import ListingService from '@/network/services/listing'
import { FC } from 'react'
import { useParams } from 'react-router-dom'

const ListingDetails: FC = () => {
  const { listing_id } = useParams()

  if (!listing_id) {
    return <></>
  }

  return <View {...{ listing_id }} />
}

const View: FC<{ listing_id: string }> = ({ listing_id }) => {
  const { listing, isLoading } = useListing(listing_id)

  return (
    <div className="space-y-6">
      <div className="flex h-full flex-1 flex-col space-y-4 p-4 md:p-8">
        {!isLoading && (
          <>
            <div className="flex w-full justify-end space-x-2">
              <DeleteButton
                name="Listing"
                id={listing_id}
                deleteService={ListingService.clientDelete}
                deleteSuccessRoute={'/listings'}
              />
            </div>
            <div className="grid grid-cols-[6fr] gap-4">
              <ListingCard listing={listing} />
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ListingDetails
