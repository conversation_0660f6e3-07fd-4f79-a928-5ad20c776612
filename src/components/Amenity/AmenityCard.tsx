import FormDialog from '@/components/Form/FormDialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Amenity } from '@/types/Amenity'
import { FC, useState } from 'react'
import AmenityForm from './AmenityForm'
import { Content, Title } from '@/components/common'

const AmenityCard: FC<{ amenity?: Amenity }> = ({ amenity }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>{amenity?.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>ID</Title>
            <Content>{amenity?.id}</Content>
            <Title>Description</Title>
            <Content>{amenity?.description}</Content>
            <Title>Is Hidden</Title>
            <Content>{amenity?.is_hidden?.toString()}</Content>
            <Title>Slug</Title>
            <Content>{amenity?.slug}</Content>
            <Title>Font Icon Name</Title>
            <Content>{amenity?.font_icon_name}</Content>
          </div>
        </CardContent>
      </Card>

      <FormDialog title="Edit Amenity" {...{ open: isDialogOpen, setOpen: setIsDialogOpen }}>
        <AmenityForm amenity={amenity} />
      </FormDialog>
    </>
  )
}

export default AmenityCard
