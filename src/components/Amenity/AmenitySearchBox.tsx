import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { FormItem, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import AmenityService from '@/network/services/amenity'
import { Amenity } from '@/types/Amenity'
import { useSearchAmenity } from '@/hooks/search/useSearchAmenity'

// multiple select
export const AmenitySearchBox: FC<{
  slug: string
  value: string[]
  onChange: (d: string[]) => void
  index: number
}> = ({ slug, value, onChange, index }) => {
  const [open, setOpen] = useState(false)
  const [amenity, setAmenity] = useState<Amenity>()

  useEffect(() => {
    const fetchAmenity = async () => {
      const amenityData = await AmenityService.clientGetSingleSlug(slug)

      if (amenityData.data.data) {
        setAmenity(amenityData.data.data)
      }
    }

    if (slug) {
      fetchAmenity()
    }
  }, [slug])

  const handleSetActive = (amenity: Amenity) => {
    if (amenity?.slug != slug) {
      setAmenity(amenity)
      const newValue = [...value]
      newValue[index] = amenity.slug
      onChange(newValue)
    } else {
      setAmenity(undefined)
      const newValue = [...value]
      newValue[index] = ''
      onChange(newValue)
    }
  }

  const displayName = !isEmpty(amenity) ? amenity?.name : 'Choose a amenity'

  return (
    <FormItem className="flex flex-col pb-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(amenity) && 'text-muted-foreground',
                'w-full'
              )}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<Amenity, Amenity>
            fn={useSearchAmenity}
            renderFn={(prod: Amenity) => `${prod.name} (${prod.slug})`}
            valueFn={(prod: Amenity) => prod.slug}
            compareFn={(prod: Amenity) => {
              if (isEmpty(amenity)) {
                return false
              }

              const findIndex = amenity?.slug == prod.slug

              return findIndex
            }}
            selectedResult={amenity}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
            excludeId={value}
            useSlug
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
