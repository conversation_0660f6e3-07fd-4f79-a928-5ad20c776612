import { Amenity, AmenityResponse } from '@/types/Amenity'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import AmenityService from '@/network/services/amenity'

const columns: ColumnDef<Amenity>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'description',
    header: 'Description'
  },
  {
    accessorKey: 'slug',
    header: 'Slug'
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'name',
    header: 'Name',
    dataType: 'string'
  },
  {
    columnKey: 'description',
    header: 'Description',
    dataType: 'string'
  },
  {
    columnKey: 'slug',
    header: 'Slug',
    dataType: 'string'
  }
]

const AmenityTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<Amenity, unknown, AmenityResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={AmenityService.getAll}
        toRow={AmenityService.toRow}
        toPaginate={AmenityService.toPaginate}
        onRowClick={(row: Row<Amenity>) => {
          nav(`/amenities/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={['id', 'name', 'description', 'slug']}
      />
    </>
  )
}

export default AmenityTable
