import { FC } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'

const ImageCard: FC<{ image_url: string }> = ({ image_url }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Image</CardTitle>
      </CardHeader>
      <CardContent>
        {image_url && (
          <div>
            <img
              src={image_url}
              alt="Image"
              style={{ objectFit: 'contain', pointerEvents: 'none' }}
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default ImageCard
