import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { DateTime } from 'luxon'
import { EventCategoryTag, EventInfo, EventInfoResponse } from '@/types/EventInfo'
import EventInfoService from '@/network/services/eventInfo'
import { serialize } from '@/network/request'

const columns: ColumnDef<EventInfo>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'thumbnail',
    header: 'Thumbnail',
    cell: (props) => {
      return (
        <img
          className="w-[100px] float-right"
          src={props.getValue<string>()}
          alt="event-info-image"
        />
      )
    }
  },
  {
    accessorKey: 'title',
    header: 'Title'
  },
  {
    accessorKey: 'description',
    header: 'Description'
  },
  {
    accessorKey: 'created_by',
    header: 'Created By'
  },
  {
    accessorKey: 'updated_by',
    header: 'Updated By',
    cell: (props) => {
      return props.getValue<String>() ?? '-'
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'number'
  },
  {
    columnKey: 'title',
    header: 'Title',
    dataType: 'string'
  },
  {
    columnKey: 'description',
    header: 'Description',
    dataType: 'string'
  },
  {
    columnKey: 'created_by',
    header: 'Created By',
    dataType: 'string'
  },
  {
    columnKey: 'updated_by',
    header: 'Updated By',
    dataType: 'string'
  }
]

function EventInfoTable({ categoryTag }: { categoryTag: EventCategoryTag }) {
  const nav = useNavigate()

  return (
    <>
      <DataTable<EventInfo, unknown, EventInfoResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={serialize(EventInfoService.getAll, { category_tag: categoryTag })}
        toRow={EventInfoService.toRow}
        toPaginate={EventInfoService.toPaginate}
        onRowClick={(row: Row<EventInfo>) => {
          nav(`/announcements/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={[
          'id',
          'title',
          'description',
          'created_by',
          'updated_by',
          'created_at',
          'updated_at'
        ]}
      />
    </>
  )
}

export default EventInfoTable
