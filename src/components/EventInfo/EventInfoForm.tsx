import { FC, useContext, useState } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import { CreateEventInfo, EventCategoryTag, EventInfo } from '@/types/EventInfo'
import EventInfoService from '@/network/services/eventInfo'
import EditorComponent from '../Editor/Editor'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { title } from 'radash'

const EventInfoForm: FC<{ eventInfo?: EventInfo }> = ({ eventInfo }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<CreateEventInfo>({
    shouldUseNativeValidation: false,
    defaultValues: eventInfo
  })

  const [imagePreview, setImagePreview] = useState<string>(eventInfo?.thumbnail ?? '')

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!eventInfo) {
        await EventInfoService.clientCreate({ ...values })

        toast({
          title: 'Success',
          description: 'Create announcement successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate((key) => typeof key === 'string' && key.startsWith(EventInfoService.getAll))
      } else {
        await EventInfoService.clientUpdate(eventInfo.id, values)

        toast({
          title: 'Success',
          description: 'Update announcement successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(EventInfoService.getSingle(eventInfo.id), {}))
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="event-info">
        <FormField
          control={control}
          name="title"
          rules={{ required: 'Please enter the announcement title' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Announcement Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="description"
          rules={{ required: 'Please enter the announcement description' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Announcement Description {eventInfo?.category_tag}</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="category_tag"
          rules={{ required: 'Please enter the announcement category' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Announcement Category</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={(value) => field.onChange(value)}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select announcement category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Category Options</SelectLabel>
                      {Object.keys(EventCategoryTag).map((tag) => {
                        return (
                          <SelectItem value={EventCategoryTag[tag as keyof typeof EventCategoryTag]}>
                            {title(EventCategoryTag[tag as keyof typeof EventCategoryTag])}
                          </SelectItem>
                        )
                      })}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="content"
          rules={{ required: 'Please enter the content' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Content</FormLabel>
              <FormControl>
                <EditorComponent content={field.value} onChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="image_file"
          rules={eventInfo ? {} : { required: 'Please provide the announcement image' }}
          render={({ field }) => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Image</FormLabel>
              <div className="flex w-full space-x-2 ">
                <div className="flex-1">
                  {imagePreview && (
                    <img className="w-[180px] py-4" src={imagePreview} alt="announcement-image" />
                  )}
                  <FormControl>
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        if (e.target.files) {
                          field.onChange(e.target.files[0])
                          setImagePreview(URL.createObjectURL(e.target.files[0]))
                        }
                      }}
                    />
                  </FormControl>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default EventInfoForm
