import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { DateTime } from 'luxon'
import { EventCategoryTag, EventInfo } from '@/types/EventInfo'
import ReadOnlyEditorComponent from '../Editor/ReadOnlyEditor'
import { parseContent } from '@/lib/utils'

const EventInfoCard: FC<{ eventInfo?: EventInfo }> = ({ eventInfo }) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{eventInfo?.id}</Content>
            <Title>Thumbnail</Title>
            <Content>
              {eventInfo?.thumbnail ? (
                <img
                  className="w-[250px] float-right"
                  src={eventInfo?.thumbnail}
                  alt="event-info-image"
                />
              ) : (
                <>-</>
              )}
            </Content>
            <Title>Title</Title>
            <Content>{eventInfo?.title}</Content>
            <Title>Description</Title>
            <Content>{eventInfo?.description}</Content>
            <Title>Category Tag</Title>
            <Content>
              {
                EventCategoryTag[
                  eventInfo?.category_tag.toString() as keyof typeof EventCategoryTag
                ]
              }
            </Content>
            <Title>Created By</Title>
            <Content>{eventInfo?.created_by}</Content>
            <Title>Updated By</Title>
            <Content>{eventInfo?.updated_by ?? '-'}</Content>
            <Title>Created At</Title>
            <Content>
              {eventInfo?.created_at?.toLocaleString(DateTime.DATETIME_SHORT) ?? '-'}
            </Content>
            <Title>Updated At</Title>
            <Content>
              {eventInfo?.updated_at?.toLocaleString(DateTime.DATETIME_SHORT) ?? '-'}
            </Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Content</CardTitle>
        </CardHeader>
        <CardContent>
          <ReadOnlyEditorComponent content={parseContent(eventInfo?.content ?? '')} />
        </CardContent>
      </Card>
    </>
  )
}

export default EventInfoCard
