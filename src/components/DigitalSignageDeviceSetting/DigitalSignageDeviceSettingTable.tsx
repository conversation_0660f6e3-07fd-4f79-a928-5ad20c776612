import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { DateTime } from 'luxon'
import {
  DigitalSignageDeviceSetting,
  DigitalSignageDeviceSettingResponse
} from '@/types/DigitalSignageDeviceSetting'
import DigitalSignageDeviceService from '@/network/services/digitalSignageDeviceSetting'

const columns: ColumnDef<DigitalSignageDeviceSetting>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'created_by',
    header: 'Created By'
  },
  {
    accessorKey: 'updated_by',
    header: 'Updated By',
    cell: (props) => {
      return props.getValue<String>() ?? '-'
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'number'
  }
]

const DigitalSignageDeviceSettingTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<DigitalSignageDeviceSetting, unknown, DigitalSignageDeviceSettingResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={DigitalSignageDeviceService.getAll}
        toRow={DigitalSignageDeviceService.toRow}
        toPaginate={DigitalSignageDeviceService.toPaginate}
        onRowClick={(row: Row<DigitalSignageDeviceSetting>) => {
          nav(`/digital-signage-device-settings/${row.original.id}`)
        }}
      />
    </>
  )
}

export default DigitalSignageDeviceSettingTable
