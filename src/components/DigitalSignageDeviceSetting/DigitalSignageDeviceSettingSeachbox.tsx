import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import { useFormContext } from 'react-hook-form'
import DigitalSignageMediaService from '@/network/services/digitalSignageMedia'
import { DigitalSignageMedia } from '@/types/DigitalSignageMedia'
import { useSearchDigitalSignageMedia } from '@/hooks/search/useSearchDigitalSignageMedia'

// multiple select
export const DigitalSignageMediaSearchBox: FC<{
  id: string
  name: string
}> = ({ id, name }) => {
  const [open, setOpen] = useState(false)
  const [media, setMedia] = useState<DigitalSignageMedia>()
  const form = useFormContext()

  useEffect(() => {
    const fetchData = async () => {
      const clientData = await DigitalSignageMediaService.clientGetSingle(id)

      if (clientData.data.data) {
        setMedia(clientData.data.data)
      }
    }

    if (id) {
      fetchData()
    }
  }, [id])

  const handleSetActive = (media: DigitalSignageMedia) => {
    setMedia(media)
    form.setValue(name, media.id)
  }

  const displayName = !isEmpty(media) ? media?.name : 'Choose a device media'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Device Associated Setting ID</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(media) && 'text-muted-foreground',
                'w-full'
              )}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<DigitalSignageMedia, DigitalSignageMedia>
            fn={useSearchDigitalSignageMedia}
            renderFn={(media: DigitalSignageMedia) => media.name}
            valueFn={(media: DigitalSignageMedia) => media.id.toString()}
            compareFn={(media: DigitalSignageMedia) => {
              if (isEmpty(media)) {
                return false
              }

              const findIndex = media?.id == media.id

              return findIndex
            }}
            selectedResult={media}
            onSelectResult={handleSetActive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
