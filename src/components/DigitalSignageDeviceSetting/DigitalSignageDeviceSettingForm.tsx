import { FC, useContext } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import {
  CreateDigitalSignageDeviceSetting,
  DigitalSignageDeviceSetting
} from '@/types/DigitalSignageDeviceSetting'
import DigitalSignageDeviceSettingService from '@/network/services/digitalSignageDeviceSetting'
import { DigitalSignageMediaSearchBox } from './DigitalSignageDeviceSettingSeachbox'

const DigitalSignageDeviceSettingForm: FC<{ deviceSetting?: DigitalSignageDeviceSetting }> = ({
  deviceSetting
}) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<CreateDigitalSignageDeviceSetting>({
    shouldUseNativeValidation: false,
    defaultValues: deviceSetting
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!deviceSetting) {
        await DigitalSignageDeviceSettingService.clientCreate({
          ...values
        })

        toast({
          title: 'Success',
          description: 'Create device setting successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(DigitalSignageDeviceSettingService.getAll)
        )
      } else {
        await DigitalSignageDeviceSettingService.clientUpdate(deviceSetting.id, values)

        toast({
          title: 'Success',
          description: 'Update device setting successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(
              serialize(DigitalSignageDeviceSettingService.getSingle(deviceSetting.id), {})
            )
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="digital-signage-device-setting">
        <FormField
          control={control}
          name="name"
          rules={{ required: 'Please enter the device setting name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Device Setting Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="media_id"
          rules={{ required: 'Please enter the device setting' }}
          render={({ field }) => {
            return (
              <DigitalSignageMediaSearchBox
                id={field.value ? field.value.toString() : ''}
                name={field.name}
              />
            )
          }}
        />
      </form>
    </Form>
  )
}

export default DigitalSignageDeviceSettingForm
