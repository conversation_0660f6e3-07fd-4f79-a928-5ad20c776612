import { Card, CardContent } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { DigitalSignageDeviceSetting } from '@/types/DigitalSignageDeviceSetting'
import { DateTime } from 'luxon'

const DigitalSignageDeviceSettingCard: FC<{ deviceSetting?: DigitalSignageDeviceSetting }> = ({
  deviceSetting
}) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{deviceSetting?.id}</Content>
            <Title>Name</Title>
            <Content>{deviceSetting?.name}</Content>
            <Title>Linked Media Name</Title>
            <Content>{deviceSetting?.media.name}</Content>
            <Title>Updated By</Title>
            <Content>{deviceSetting?.updated_by ?? '-'}</Content>
            <Title>Created By</Title>
            <Content>{deviceSetting?.created_by}</Content>
            <Title>Created At</Title>
            <Content>{deviceSetting?.created_at?.toLocaleString(DateTime.DATETIME_SHORT)}</Content>
            <Title>Updated At</Title>
            <Content>{deviceSetting?.updated_at?.toLocaleString(DateTime.DATETIME_SHORT)}</Content>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default DigitalSignageDeviceSettingCard
