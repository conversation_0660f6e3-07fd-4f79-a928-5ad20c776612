import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import PriceMatchRequestService from '@/network/services/priceMatchRequest'
import {
  PriceMatchRequest,
  PriceMatchRequestResponse,
  PriceMatchRequestStatus
} from '@/types/PriceMatchRequest'
import { DateTime } from 'luxon'
import { serialize } from '@/network/request'

const columns: ColumnDef<PriceMatchRequest>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'user.full_name',
    header: 'Customer'
  },
  {
    accessorKey: 'user.email_address',
    header: 'Email'
  },
  {
    accessorKey: 'order_number',
    header: 'Order'
  },
  {
    accessorKey: 'product_name',
    header: 'Product'
  },
  // {
  //   accessorKey: 'product_brand',
  //   header: 'Brand'
  // },
  // {
  //   accessorKey: 'product_model_number',
  //   header: 'Product Model Number'
  // },
  // {
  //   accessorKey: 'retailer',
  //   header: 'Retailer'
  // },
  // {
  //   accessorKey: 'price',
  //   header: 'Price'
  // },
  // {
  //   accessorKey: 'product_link',
  //   header: 'Link'
  // },
  {
    accessorKey: 'comment',
    header: 'Comment'
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  // {
  //   accessorKey: 'updated_at',
  //   header: 'Updated At',
  //   cell: (props) => {
  //     return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
  //   }
  // },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: (props) => {
      const status = props.getValue<PriceMatchRequestStatus>()
      return (
        <>
          {' '}
          <span
            style={{
              display: 'inline-block',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              marginRight: '8px',
              backgroundColor:
                status == PriceMatchRequestStatus.APPROVE
                  ? '#4CAF50'
                  : status == PriceMatchRequestStatus.PENDING
                  ? '#FFA500'
                  : '#F44336'
            }}
          ></span>
          {status.toUpperCase()}
        </>
      )
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'user.full_name',
    header: 'Customer',
    dataType: 'string',
    apiKey: 'user'
  },
  {
    columnKey: 'product_name',
    header: 'Product',
    dataType: 'string'
  }
  // {
  //   columnKey: 'product_brand',
  //   header: 'Brand',
  //   dataType: 'string'
  // },
  // {
  //   columnKey: 'retailer',
  //   header: 'Retailer',
  //   dataType: 'string'
  // },
  // {
  //   columnKey: 'price',
  //   header: 'Price',
  //   dataType: 'number'
  // }
]

const PriceMatchRequestTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<PriceMatchRequest, unknown, PriceMatchRequestResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={serialize(PriceMatchRequestService.getAll, { include_user: true })}
        toRow={PriceMatchRequestService.toRow}
        toPaginate={PriceMatchRequestService.toPaginate}
        onRowClick={(row: Row<PriceMatchRequest>) => {
          nav(`/price-match-requests/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={[
          'id',
          'customer_name', // TODO: Pending sync backend user info from gomama_core to gomama_cms
          'customer_email', // TODO: Pending sync backend user info from gomama_core to gomama_cms
          'order_number',
          'product_name',
          'product_brand',
          // 'product_model_number',
          // 'retailer',
          // 'price',
          'product_link',
          'comment',
          'created_at'
          // 'updated_at'
        ]}
      />
    </>
  )
}

export default PriceMatchRequestTable
