import { Card, CardContent } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { PriceMatchRequest, PriceMatchRequestStatus } from '@/types/PriceMatchRequest'

const PriceMatchRequestCard: FC<{ priceMatchRequest?: PriceMatchRequest }> = ({
  priceMatchRequest
}) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{priceMatchRequest?.id}</Content>
            <Title>Customer Name</Title>
            <Content>{priceMatchRequest?.user?.full_name}</Content>
            <Title>Customer Email</Title>
            <Content>{priceMatchRequest?.user?.email_address}</Content>
            <Title>Order Number</Title>
            <Content>{priceMatchRequest?.order_number}</Content>
            <Title>Product Name</Title>
            <Content>{priceMatchRequest?.product_name}</Content>
            <Title>Product Brand</Title>
            <Content>{priceMatchRequest?.product_brand}</Content>
            <Title>Product Model Number</Title>
            <Content>{priceMatchRequest?.product_model_number}</Content>
            <Title>Retailer</Title>
            <Content>{priceMatchRequest?.retailer}</Content>
            <Title>Price</Title>
            <Content>{priceMatchRequest?.price}</Content>
            <Title>Product Link</Title>
            <Content>{priceMatchRequest?.product_link}</Content>
            <Title>Comment</Title>
            <Content>{priceMatchRequest?.comment}</Content>
            <Title>Reviewed Status</Title>
            <Content>
              <span
                style={{
                  display: 'inline-block',
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  marginRight: '8px',
                  backgroundColor:
                    priceMatchRequest?.status == PriceMatchRequestStatus.APPROVE
                      ? '#4CAF50'
                      : priceMatchRequest?.status == PriceMatchRequestStatus.PENDING
                      ? '#FFA500'
                      : '#F44336'
                }}
              ></span>
              {priceMatchRequest?.status.toUpperCase()}
            </Content>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default PriceMatchRequestCard
