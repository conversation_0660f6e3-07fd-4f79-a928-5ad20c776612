import { FC, useState } from 'react'
import { Button } from '../ui/button'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '../ui/alert-dialog'
import { useToast } from '../ui/use-toast'
import { AxiosResponse } from 'axios'
import { ToastAction } from '../ui/toast'
import { VerifiedIcon, Pen, X } from 'lucide-react'
import { PriceMatchRequestStatus, UpdatePriceMatchRequest } from '@/types/PriceMatchRequest'
import { useNavigate } from 'react-router-dom'

const ReviewButton: FC<{
  id: string
  reviewService: (id: string | number, data: UpdatePriceMatchRequest) => Promise<AxiosResponse>
  name: string
  reviewSuccessRoute?: string
  priceMatchRequestStatus: PriceMatchRequestStatus
}> = ({ id, reviewService, name, reviewSuccessRoute = '/', priceMatchRequestStatus }) => {
  const { toast } = useToast()
  const [isOpen, setIsOpen] = useState(false)
  const navigate = useNavigate()

  const handleReview = async () => {
    try {
      await reviewService(id, { status: priceMatchRequestStatus })

      toast({
        title: 'Success',
        description: `${name} status update successfully successfully.`,
        action: <ToastAction altText="OK">OK</ToastAction>
      })

      // TODO: After set should refetch the price-match-request data.
      setIsOpen(false)
      navigate(reviewSuccessRoute)
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  }

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        variant={
          priceMatchRequestStatus == PriceMatchRequestStatus.APPROVE
            ? 'success'
            : priceMatchRequestStatus == PriceMatchRequestStatus.PENDING
            ? 'default'
            : 'destructive'
        }
      >
        {priceMatchRequestStatus == PriceMatchRequestStatus.APPROVE && (
          <VerifiedIcon className="h-4 w-4" />
        )}
        {priceMatchRequestStatus == PriceMatchRequestStatus.PENDING && <Pen className="h-4 w-4" />}
        {priceMatchRequestStatus == PriceMatchRequestStatus.REJECT && <X className="h-4 w-4" />}

        {priceMatchRequestStatus.toUpperCase()}
      </Button>

      <AlertDialog open={isOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Review {name}</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure to set this as {priceMatchRequestStatus.toUpperCase()}?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button onClick={() => setIsOpen(false)}>Cancel</Button>

            <Button onClick={handleReview}>Confirm</Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default ReviewButton
