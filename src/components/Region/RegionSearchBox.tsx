import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import RegionService from '@/network/services/region'
import { Region } from '@/types/Region'
import { useSearchRegion } from '@/hooks/search/useSearchRegion'

// multiple select
export const RegionSearchBox: FC<{ slug: string; name: string }> = ({ slug, name }) => {
  const [open, setOpen] = useState(false)
  // const [hasValue, setHasValue] = useState(false)
  const [region, setRegion] = useState<Region>()
  const form = useFormContext()

  useEffect(() => {
    const fetchRegion = async () => {
      const regionData = await RegionService.clientGetSingleSlug(slug)

      if (regionData.data.data) {
        setRegion(regionData.data.data)
      }
    }

    if (slug) {
      fetchRegion()
    }
  }, [slug])

  // useEffect(() => {
  //   if (slug) {
  //     setHasValue(true)
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [])

  const handleSetActive = (region: Region) => {
    setRegion(region)
    form.setValue(name, region.slug)
  }

  const displayName = !isEmpty(region) ? region?.name : 'Choose a region'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Region</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(region) && 'text-muted-foreground',
                'w-full'
              )}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<Region, Region>
            fn={useSearchRegion}
            renderFn={(prod: Region) => prod.name}
            valueFn={(prod: Region) => prod.slug}
            compareFn={(prod: Region) => {
              if (isEmpty(region)) {
                return false
              }

              const findIndex = region?.slug == prod.slug

              return findIndex
            }}
            selectedResult={region}
            onSelectResult={handleSetActive}
            useSlug
            // onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
