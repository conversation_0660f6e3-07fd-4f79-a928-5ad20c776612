import { FC, useContext, useState } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import { CreateShopBanner, ShopBanner } from '@/types/ShopBanner'
import ShopBannerService from '@/network/services/shopBanner'

const ShopBannerForm: FC<{ shopBanner?: ShopBanner }> = ({ shopBanner }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<CreateShopBanner>({
    shouldUseNativeValidation: false,
    defaultValues: shopBanner
  })

  const [imagePreview, setImagePreview] = useState<string>(shopBanner?.image_url ?? '')

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!shopBanner) {
        await ShopBannerService.clientCreate({ ...values })

        toast({
          title: 'Success',
          description: 'Create Shop Banner successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate((key) => typeof key === 'string' && key.startsWith(ShopBannerService.getAll))
      } else {
        await ShopBannerService.clientUpdate(shopBanner.id, values)

        toast({
          title: 'Success',
          description: 'Update Shop Banner successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ShopBannerService.getSingle(shopBanner.id), {}))
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="shop-banner">
        <FormField
          control={control}
          name="banner_name"
          rules={{ required: 'Please enter the shop banner name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Shop Banner Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="action_link"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Action Link (Optional)</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="image_file"
          rules={shopBanner ? {} : { required: 'Please provide the shop banner image' }}
          render={({ field }) => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Image</FormLabel>
              <div className="flex w-full space-x-2 ">
                <div className="flex-1">
                  {imagePreview && (
                    <img className="w-[180px] py-4" src={imagePreview} alt="shop-banner-image" />
                  )}
                  <FormControl>
                    <Input
                      type="file"
                      onChange={(e) => {
                        if (e.target.files) {
                          field.onChange(e.target.files[0])
                          setImagePreview(URL.createObjectURL(e.target.files[0]))
                        }
                      }}
                    />
                  </FormControl>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default ShopBannerForm
