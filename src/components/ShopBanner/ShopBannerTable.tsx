import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import ShopBannerService from '@/network/services/shopBanner'
import { ShopBanner, ShopBannerResponse } from '@/types/ShopBanner'
import { DateTime } from 'luxon'

const columns: ColumnDef<ShopBanner>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'banner_name',
    header: 'Banner Name'
  },
  {
    accessorKey: 'action_link',
    header: 'Action Link'
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'banner_name',
    header: 'Banner Name',
    dataType: 'string'
  }
]

const ShopBannerTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<ShopBanner, unknown, ShopBannerResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={ShopBannerService.getAll}
        toRow={ShopBannerService.toRow}
        toPaginate={ShopBannerService.toPaginate}
        onRowClick={(row: Row<ShopBanner>) => {
          nav(`/shop-banners/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={['id', 'banner_name', 'action_link', 'created_at', 'updated_at']}
      />
    </>
  )
}

export default ShopBannerTable
