import { Card, CardContent } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { ShopBanner } from '@/types/ShopBanner'
import { DateTime } from 'luxon'

const ShopBannerCard: FC<{ shopBanner?: ShopBanner }> = ({ shopBanner }) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{shopBanner?.id}</Content>
            <Title>Banner Name</Title>
            <Content>{shopBanner?.banner_name}</Content>
            <Title>Image Url</Title>
            <Content>
              {shopBanner?.image_url ? (
                <img
                  className="w-[250px] float-right"
                  src={shopBanner?.image_url}
                  alt="shop-banner-image"
                />
              ) : (
                <>-</>
              )}
            </Content>
            <Title>Action Link</Title>
            <Content>{shopBanner?.action_link}</Content>
            <Title>Created At</Title>
            <Content>
              {shopBanner ? shopBanner.created_at.toLocaleString(DateTime.DATETIME_SHORT) : '-'}
            </Content>
            <Title>Updated At</Title>
            <Content>
              {shopBanner ? shopBanner.updated_at.toLocaleString(DateTime.DATETIME_SHORT) : '-'}
            </Content>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default ShopBannerCard
