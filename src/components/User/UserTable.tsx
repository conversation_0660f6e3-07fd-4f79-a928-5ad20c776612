import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { User, UserResponse } from '@/types/User'
import UserService from '@/network/services/user'
import { cn, statusToColor } from '@/lib/utils'

const columns: ColumnDef<User>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'username',
    header: 'Username'
  },
  {
    accessorKey: 'full_name',
    header: 'Full Name'
  },
  {
    accessorKey: 'email_address',
    header: 'Email'
  },
  {
    accessorKey: 'full_mobile_number',
    header: 'Mobile Number'
  },
  {
    accessorKey: 'country_name',
    header: 'Country'
  },
  {
    accessorKey: 'auth_provider',
    header: 'Auth Type'
  },
  {
    accessorKey: 'deleted_at',
    header: 'Active Status',
    cell: (props) => {
      const value = props.getValue<string>()
      const color = statusToColor(value ? 'rejected' : 'approved')

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value ? 'Deleted' : 'Active'}</span>
        </div>
      )
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'username',
    header: 'Username',
    dataType: 'string'
  },
  {
    columnKey: 'full_name',
    header: 'Full Name',
    dataType: 'string'
  },
  {
    columnKey: 'email_address',
    header: 'Email',
    dataType: 'string'
  },
  {
    columnKey: 'country_name',
    header: 'Country',
    dataType: 'string'
  },
  {
    columnKey: 'auth_provider',
    header: 'Auth Type',
    dataType: 'string'
  }
]

const UserTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<User, unknown, UserResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={UserService.getUsers}
        toRow={UserService.toRow}
        toPaginate={UserService.toPaginate}
        onRowClick={(row: Row<User>) => {
          nav(`/users/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={[
          'id',
          'username',
          'full_name',
          'full_mobile_number',
          'email_address',
          'country_name'
        ]}
      />
    </>
  )
}

export default UserTable
