import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { User } from '@/types/User'
import { FC } from 'react'
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs'
import ImageCard from '../Common/ImageCard'
import { RotateCcw } from 'lucide-react'
import { Button } from '../ui/button'
import { cn, statusToColor } from '@/lib/utils'
import UserService from '@/network/services/user'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DateTime } from 'luxon'
import { Content, Title } from '@/components/common'
import { CopyButton } from '../ui/copy-button'

const UserCard: FC<{ user?: User }> = ({ user }) => {
  return (
    <>
      <Tabs
        defaultValue="user"
        className="space-y-4"
        enableUrlHash
        tabValues={["user", "verification", "device"]}
      >
        <TabsList>
          <TabsTrigger value="user">User</TabsTrigger>
          <TabsTrigger value="verification">Verification</TabsTrigger>
          <TabsTrigger value="device">Devices</TabsTrigger>
        </TabsList>

        <TabsContent value="user">
          <div className="space-y-4">
            <UserView user={user} />
          </div>
        </TabsContent>

        <TabsContent value="verification">
          <div className="space-y-4">
            <VerificationView user={user} />
          </div>
        </TabsContent>

        <TabsContent value="device">
          <div className="space-y-4">
            <DeviceView user={user} />
          </div>
        </TabsContent>
      </Tabs>
    </>
  )
}

const UserView: FC<{ user?: User }> = ({ user }) => {
  return (
    <>
      <div className="grid  md:grid-cols-[4fr_2fr] gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0">
            <CardTitle>Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <Title>Email</Title>
              <Content>{user?.email_address}</Content>
              <Title>First Name</Title>
              <Content>{user?.first_name}</Content>
              <Title>Last Name</Title>
              <Content>{user?.last_name}</Content>
              <Title>Gender</Title>
              <Content>{user?.gender}</Content>
              <Title>Birthday</Title>
              <Content>{user?.birthday ? user.birthday.toFormat('yyyy-MM-dd') : ''}</Content>
              <Title>Mobile Number</Title>
              <Content>{user?.full_mobile_number}</Content>
              <Title>Singpass Mobile Number</Title>
              <Content>{user?.singpass_mobile_number}</Content>
              <Title>Country Name</Title>
              <Content>{user?.country_name}</Content>
              <Title>Company Name</Title>
              <Content>{user?.company_name}</Content>
              <Title>NRIC</Title>
              <Content>{user?.nric}</Content>
              <Title>Active Status</Title>
              <Content>
                {
                  <div className="flex justify-end  space-x-2">
                    <div
                      className={cn(
                        'h-1.5 w-1.5 self-center rounded-full',
                        statusToColor(user?.deleted_at ? 'rejected' : 'approved')
                      )}
                    />
                    <span className="capitalize">{user?.deleted_at ? 'Deleted' : 'Active'}</span>
                  </div>
                }
              </Content>
              {user?.deleted_at && (
                <>
                  <Title>Deleted Reason</Title>
                  <Content>{user?.delete_reason ?? '-'}</Content>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        <ImageCard image_url={user?.photo_url ?? ''} />
      </div>

      <div className="grid grid-cols-[6fr] gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0">
            <CardTitle>Children</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <Title>Children Birthday</Title>
              <div className="flex flex-column gap-2">
                {user?.children_birthday?.map((birthday) => {
                  return <Content key={birthday.toISO()}>{birthday.toFormat('yyyy-MM-dd')}</Content>
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>{user?.full_name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Email</Title>
            <Content>{user?.email_address}</Content>
            <Title>First Name</Title>
            <Content>{user?.first_name}</Content>
            <Title>Last Name</Title>
            <Content>{user?.last_name}</Content>
            <Title>Share Code</Title>
            <Content>{user?.share_code}</Content>
            <Title>Gender</Title>
            <Content>{user?.gender}</Content>
            <Title>Birthday</Title>
            <Content>{user?.birthday}</Content>
            <Title>Children Birthday</Title>
            <div className="flex flex-column gap-2">
              {user?.children_birthday?.map((birthday) => {
                return <Content key={birthday}>{birthday}</Content>
              })}
            </div>
            <Title>Mobile Number</Title>
            <Content>{user?.mobile_number}</Content>
            <Title>Company Name</Title>
            <Content>{user?.company_name}</Content>
            <Title>Country Name</Title>
            <Content>{user?.country_name}</Content>
            <Title>NRIC</Title>
            <Content>{user?.nric}</Content>
            <Title>Singpass Mobile Number</Title>
            <Content>{user?.singpass_mobile_number}</Content>
            <Title>Timezone</Title>
            <Content>{user?.timezone}</Content>
            <Title>Auth Provider</Title>
            <Content>{user?.auth_provider}</Content>
            <Title>Is Admin Verified</Title>
            <Content>{user?.is_admin_verified?.toString()}</Content>
            <Title>Is Email Verified</Title>
            <Content>{user?.is_email_address_verified?.toString()}</Content>
            <Title>Is Mobile Number Verified</Title>
            <Content>{user?.is_mobile_number_verified?.toString()}</Content>
            <Title>Is Singpass Verified</Title>
            <Content>{user?.is_singpass_verified?.toString()}</Content>
            <Title>Passport Number</Title>
            <Content>{user?.passport_number}</Content>
            <Title>Is Passport Verified</Title>
            <Content>{user?.is_passport_verified?.toString()}</Content>
            <Title>Is Gomama Verified</Title>
            <Content>{user?.is_gomama_verified?.toString()}</Content>
            <Title>Is Hidden</Title>
            <Content>{user?.is_hidden?.toString()}</Content>
            <Title>Firestore Id</Title>
            <Content>{user?.firestore_id}</Content>
            <Title>Full Mobile Number</Title>
            <Content>{user?.full_mobile_number}</Content>
            <Title>Full Singpass Mobile Number</Title>
            <Content>{user?.full_singpass_mobile_number}</Content>
          </div>
        </CardContent>
      </Card> */}
    </>
  )
}

const VerificationView: FC<{ user?: User }> = ({ user }) => {
  const { toast } = useToast()
  return (
    <>
      <div className="grid grid-cols-[6fr] gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0">
            <CardTitle>Verification</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <Title>Is Admin Verified</Title>
              <Content>{user?.is_admin_verified?.toString()}</Content>
              <Title>Is Email Verified</Title>
              <Content>{user?.is_email_address_verified?.toString()}</Content>
              <Title>Is Mobile Number Verified</Title>
              <Content>{user?.is_mobile_number_verified?.toString()}</Content>
              <Title>Is Singpass Verified</Title>
              <Content>{user?.is_singpass_verified?.toString()}</Content>
              <Title>Is Passport Verified</Title>
              <Content>{user?.is_passport_verified?.toString()}</Content>
              <Title>Is Gomama Verified</Title>
              <Content>{user?.is_gomama_verified?.toString()}</Content>
              <Title>Selfie Fail Count</Title>
              <Content>
                <Button
                  variant="outline"
                  className="h-8"
                  onClick={async (event) => {
                    event.stopPropagation()
                    try {
                      await UserService.clientResetSelfieFailCount(user?.id ?? '')
                      toast({
                        title: 'Success',
                        description: 'Reset fail count successfully.',
                        action: <ToastAction altText="OK">OK</ToastAction>
                      })
                      mutate(
                        (key) =>
                          typeof key === 'string' &&
                          key.startsWith(serialize(UserService.getUser(user?.id ?? ''), {}))
                      )
                    } catch (error) {
                      console.error(error)
                      toast({
                        title: 'Failed',
                        description: 'Something went wrong',
                        variant: 'destructive'
                      })
                    }
                  }}
                >
                  <div className="flex">
                    <div
                      className={cn('h-1.5 w-1.5 self-center rounded-full capitalize text-xs')}
                    />
                    {user?.latest_verify_selfie_fail_count}
                    <RotateCcw className="w-3 ml-3" />
                  </div>
                </Button>
              </Content>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}

const DeviceView: FC<{ user?: User }> = ({ user }) => {
  return user?.devices && user.devices.length > 0 ? (
    <>
      <div className="grid  md:grid-cols-3 gap-4">
        {user?.devices.map((device) => {
          return (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0">
                <CardTitle>
                  {`${device.device_token.slice(0, 5)}...${device.device_token.slice(-5)}`}
                  <CopyButton className="ml-2" value={device.device_token} />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  <Title>Device ID</Title>
                  <Content>{device.id}</Content>
                  <Title>Device Create Date</Title>
                  <Content>{device.created_at.toLocaleString(DateTime.DATETIME_SHORT)}</Content>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </>
  ) : (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>Devices</CardTitle>
        </CardHeader>
        <CardContent>
          <span>No device linked</span>
        </CardContent>
      </Card>
    </>
  )
}

export default UserCard
