// import { mutate } from 'swr'
import { FC, useContext } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
// import { serialize } from '@/network/request'
import { DialogContext } from '@/components/Form/FormDialog'
import { SheetContext } from '@/components/Form/FormSheet'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import UserService from '@/network/services/user'
import { CreateUserInput, User } from '@/types/User'
import { mutate } from 'swr'

const UserForm: FC<{ user?: User }> = ({ user }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const dialogContext = useContext(DialogContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<CreateUserInput>({
    shouldUseNativeValidation: false,
    defaultValues: user
      ? { email_address: user.email_address, full_name: user.full_name }
      : undefined
  })

  const {
    handleSubmit,
    reset,
    control,
    formState: { isSubmitting }
  } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!user) {
        await UserService.clientCreate(values)
        toast({
          title: 'Success',
          description: 'Create User successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate((key) => typeof key === 'string' && key.startsWith(UserService.getUsers))
      } else {
        await UserService.clientUpdate(user.id, values)
        toast({
          title: 'Success',
          description: 'Update User successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        dialogContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' && key.startsWith(serialize(UserService.getUser(user.id), {}))
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')}>
        <FormField
          control={control}
          name="email_address"
          rules={{ required: 'Please enter the email' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="full_name"
          rules={{ required: 'Please enter the full name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {!user && (
          <FormField
            control={control}
            name="password"
            rules={{ required: 'Please enter the password' }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input {...field} type="password" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <div className="mt-4">
          <div className="flex justify-start">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? <Icons.spinner className="mr-2 h-4 w-4 animate-spin" /> : 'Submit'}
            </Button>
            <div className="max-w-sm pl-2">
              <Button
                variant="secondary"
                type="button"
                onClick={() => {
                  if (drawerContext) {
                    drawerContext.setIsOpen(false)
                  }

                  if (dialogContext) {
                    dialogContext.setIsOpen(false)
                  }
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </form>
    </Form>
  )
}

export default UserForm
