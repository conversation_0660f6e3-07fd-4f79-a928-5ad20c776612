import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import UserService from '@/network/services/user'
import { User } from '@/types/User'
import { useSearchUser } from '@/hooks/search/useSearchUser'

// multiple select
export const UserSearchBox: FC<{ id: string; name: string }> = ({ id, name }) => {
  const [open, setOpen] = useState(false)
  const [hasValue, setHasValue] = useState(false)
  const [user, setUser] = useState<User>()
  const form = useFormContext()

  useEffect(() => {
    const fetchUser = async () => {
      const userData = await UserService.clientGetSingle(id)

      if (userData.data.data) {
        setUser(userData.data.data)
      }
    }

    if (id) {
      fetchUser()
    }
  }, [id])

  useEffect(() => {
    if (id) {
      setHasValue(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleSetActive = (user: User) => {
    setUser(user)
    form.setValue(name, user.id)
  }

  const displayName = !isEmpty(user) ? user?.id : 'Choose a user'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>User</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(user) && 'text-muted-foreground',
                'w-full'
              )}
              disabled={hasValue}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<User, User>
            fn={useSearchUser}
            renderFn={(prod: User) => prod.id}
            valueFn={(prod: User) => prod.id.toString()}
            compareFn={(prod: User) => {
              if (isEmpty(user)) {
                return false
              }

              const findIndex = user?.id == prod.id

              return findIndex
            }}
            selectedResult={user}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
