import { CreateListing, Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useContext } from 'react'
import { Content, Title } from '../common'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '../ui/select'

const Usage: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>Usage</CardTitle>

          <FormSheet title="Edit Usage" button="Edit Usage" edit formId="listing-usage">
            <UsageForm listing={listing} />
          </FormSheet>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Usage Durations</Title>
          <div>
            {listing?.usage_durations?.map((duration) => (
              <Content key={duration}>{duration}</Content>
            ))}
          </div>
          <Title>Usage Extension Durations</Title>
          <Content>{listing?.usage_extension_durations}</Content>
          <Title>Max Number of Usage Durations</Title>
          <Content>{listing?.max_number_of_usage_extensions}</Content>
          <Title>Usage Extendable</Title>
          <Content>{listing?.is_usage_extendable ? 'YES' : 'NO'}</Content>
        </div>
      </CardContent>
    </Card>
  )
}

const UsageForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false,
    defaultValues: {
      usage_durations: listing?.usage_durations,
      usage_extension_durations: listing?.usage_extension_durations,
      max_number_of_usage_extensions: listing?.max_number_of_usage_extensions,
      is_usage_extendable: listing?.is_usage_extendable
    }
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        await ListingService.clientUpdate(listing.id, values)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-usage">
        <FormField
          control={control}
          name="usage_durations"
          defaultValue={[]}
          rules={{ required: 'Please enter at least 1 duration' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Usage Durations</FormLabel>
              <FormControl>
                <div className="space-y-1">
                  {field.value.map((value, index) => (
                    <Input
                      key={index}
                      {...field}
                      value={value}
                      onChange={(e) => {
                        const newValue = [...field.value]
                        newValue[index] = parseInt(e.target.value)
                        field.onChange(newValue)
                      }}
                      type="number"
                    />
                  ))}
                  <Button
                    type="button"
                    onClick={() => {
                      const newValue = [...field.value, '']
                      field.onChange(newValue)
                    }}
                  >
                    Add Usage Duration
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="usage_extension_durations"
          defaultValue={[]}
          rules={{ required: 'Please enter at least 1 duration' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Usage Extension Durations</FormLabel>
              <FormControl>
                <div className="space-y-1">
                  {field.value.map((value, index) => (
                    <Input
                      key={index}
                      {...field}
                      value={value}
                      onChange={(e) => {
                        const newValue = [...field.value]
                        newValue[index] = parseInt(e.target.value)
                        field.onChange(newValue)
                      }}
                      type="number"
                    />
                  ))}
                  <Button
                    type="button"
                    onClick={() => {
                      const newValue = [...field.value, '']
                      field.onChange(newValue)
                    }}
                  >
                    Add Usage Extension Duration
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="max_number_of_usage_extensions"
          rules={{ required: 'Please enter the max number of usage extensions' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Max Number Of Usage Extensions</FormLabel>
              <FormControl>
                <Input {...field} type="number" min={1} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="is_usage_extendable"
          rules={{ required: 'Please enter the usage description' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Usage Extendable</FormLabel>
              <FormControl>
                <Select value={field.value + ''} onValueChange={(value) => field.onChange(value)}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select usage" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Usage Extendable</SelectLabel>
                      <SelectItem value={true + ''}>YES</SelectItem>
                      <SelectItem value={false + ''}>NO</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default Usage
