import { CreateListing, Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useContext } from 'react'
import { Content, Title } from '../common'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { Input } from '../ui/input'
import { RegionSearchBox } from '../Region/RegionSearchBox'

const Location: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>Location</CardTitle>

          <FormSheet title="Edit Location" button="Edit Location" edit formId="listing-location">
            <LocationForm listing={listing} />
          </FormSheet>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Longitude</Title>
          <Content>{listing?.position?.coordinate?.x}</Content>
          <Title>Latitude</Title>
          <Content>{listing?.position?.coordinate?.y}</Content>
          <Title>Region</Title>
          <Content>{listing?.region?.name}</Content>
          <Title>Address Name</Title>
          <Content>{listing?.address_name}</Content>
          <Title>Full Address</Title>
          <Content>{listing?.full_address}</Content>
          <Title>Postal Code</Title>
          <Content>{listing?.postal_code}</Content>
        </div>
      </CardContent>
    </Card>
  )
}

const LocationForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false,
    defaultValues: {
      longitude: listing?.position?.coordinate?.x,
      latitude: listing?.position?.coordinate?.y,
      region: listing?.region?.slug,
      address_name: listing?.address_name,
      full_address: listing?.full_address,
      postal_code: listing?.postal_code
    }
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        await ListingService.clientUpdate(listing.id, values)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-location">
        <FormField
          control={control}
          name="longitude"
          rules={{ required: 'Please enter the longitude' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Longitude</FormLabel>
              <FormControl>
                <Input {...field} type="number" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="latitude"
          rules={{ required: 'Please enter the latitude' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Latitude</FormLabel>
              <FormControl>
                <Input {...field} type="number" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="region"
          rules={{ required: 'Please select a region' }}
          render={({ field }) => {
            return (
              <RegionSearchBox slug={field.value ? field.value.toString() : ''} name={field.name} />
            )
          }}
        />

        <FormField
          control={control}
          name="address_name"
          rules={{ required: 'Please enter the address name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="full_address"
          rules={{ required: 'Please enter the full address' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Address</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="postal_code"
          rules={{ required: 'Please enter the postal code' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Code</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default Location
