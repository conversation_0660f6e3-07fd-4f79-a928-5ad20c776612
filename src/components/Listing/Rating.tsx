import { Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import ListingRatingTable from './ListingRatingTable'

const Rating: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>Rating</CardTitle>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Average Experience Ratings</Title>
            <Content>{listing?.average_experience_ratings}</Content>
            <Title>Total Experience Ratings</Title>
            <Content>{listing?.total_experience_ratings}</Content>
            <Title>Total Sessions</Title>
            <Content>{listing?.total_sessions}</Content>
          </div>
        </CardContent>
      </Card>

      <ListingRatingTable listingId={listing?.id} />
    </>
  )
}

export default Rating
