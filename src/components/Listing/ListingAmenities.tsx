import { CreateListing, Listing } from '@/types/Listing'
import { FC, useContext, useEffect } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table'
import { Amenity } from '@/types/Amenity'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { AmenitySearchBox } from '../Amenity/AmenitySearchBox'
import { Button } from '../ui/button'

const ListingAmenities: FC<{ listing?: Listing }> = ({ listing }) => {
  const tableRow = [
    {
      accessorKey: 'name',
      header: 'Name'
    },
    {
      accessorKey: 'slug',
      header: 'Slug'
    },
    {
      accessorKey: 'description',
      header: 'Description'
    }
  ]

  return (
    <>
      <div className="flex w-full justify-end">
        <FormSheet title="Edit Amenities" button="Edit Amenities" edit formId="listing-amenities">
          <AmenitiesForm listing={listing} />
        </FormSheet>
      </div>
      <div className="rounded-md border max-w-[calc(100vw-32px)] lg:max-w-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {tableRow?.map((header) => {
                return <TableHead key={header.header}>{header.header}</TableHead>
              })}
            </TableRow>
          </TableHeader>
          <TableBody>
            {listing?.amenities?.length ?? 0 > 0 ? (
              <>
                {listing?.amenities?.map((item) => {
                  return (
                    <TableRow key={item.id}>
                      {tableRow?.map((row) => {
                        const data = item[row.accessorKey as keyof Amenity]
                        return (
                          <TableHead key={row.accessorKey}>
                            {typeof data !== 'object' ? data : ''}
                          </TableHead>
                        )
                      })}
                    </TableRow>
                  )
                })}
              </>
            ) : (
              <TableRow>
                <TableCell colSpan={tableRow.length} className="h-24 text-center">
                  No amenities.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </>
  )
}

const AmenitiesForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false
  })

  const { handleSubmit, reset, setValue, control } = form

  useEffect(() => {
    if (listing) {
      if (listing.amenities) {
        const amenitiesId = listing.amenities?.map((amenity) => {
          return amenity.slug
        })
        setValue('amenities', amenitiesId)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listing])

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        const amenities = {
          amenities: values['amenities'].map((activity) => {
            return {
              amenity_slug: activity,
              is_hidden: false
            }
          })
        }

        await ListingService.clientUpdateActivitiesAmenities(listing.id, amenities)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-amenities">
        <FormField
          control={control}
          name="amenities"
          defaultValue={[]}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Amenities</FormLabel>
              <FormControl>
                <div className="space-y-1">
                  {field.value.map((value, index) => (
                    <div key={value} className="relative">
                      <AmenitySearchBox
                        key={index}
                        slug={value ? value.toString() : ''}
                        value={field.value}
                        onChange={field.onChange}
                        index={index}
                      />

                      <Button
                        className="absolute top-0  right-10 bg-transparent text-black border-none shadow-transparent hover:text-white"
                        onClick={() => {
                          const newValue = [...field.value]
                          newValue.splice(index, 1)
                          field.onChange(newValue)
                        }}
                        type="button"
                      >
                        X
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    onClick={() => {
                      const newValue = [...field.value, '']
                      field.onChange(newValue)
                    }}
                  >
                    Add Amenity
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default ListingAmenities
