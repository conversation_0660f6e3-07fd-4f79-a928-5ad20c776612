import { Listing, ListingResponse } from '@/types/Listing'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import ListingService from '@/network/services/listing'

const columns: ColumnDef<Listing>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'description',
    header: 'Description'
  },
  {
    accessorKey: 'status',
    header: 'Status'
  },
  {
    accessorKey: 'is_hidden',
    header: 'Hide Status',
    cell: (props) => {
      return (
        <>
          {' '}
          <span
            style={{
              display: 'inline-block',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              marginRight: '8px',
              backgroundColor: props.getValue<boolean>() ? '#F44336' : '#4CAF50'
            }}
          ></span>
          {props.getValue<boolean>() ? 'Hidden' : 'Unhidden'}
        </>
      )
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'name',
    header: 'Name',
    dataType: 'string'
  },
  {
    columnKey: 'description',
    header: 'Description',
    dataType: 'string'
  },
  {
    columnKey: 'status',
    header: 'Status',
    dataType: 'listing_status_select'
  }
]

const ListingTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<Listing, unknown, ListingResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={ListingService.getAll}
        toRow={ListingService.toRow}
        toPaginate={ListingService.toPaginate}
        onRowClick={(row: Row<Listing>) => {
          nav(`/listings/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={['id', 'name', 'description', 'status']}
      />
    </>
  )
}

export default ListingTable
