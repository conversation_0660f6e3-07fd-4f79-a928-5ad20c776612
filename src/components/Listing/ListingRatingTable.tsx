import { FC, useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { ListingRatings, ListingRatingsResponse } from '@/types/ListingRatings'
import ListingRatingService from '@/network/services/listingRating'
import { DateTime } from 'luxon'

interface ListingRatingTableProps {
  listingId?: string
}

const ListingRatingTable: FC<ListingRatingTableProps> = ({ listingId }) => {
  // Define columns for the DataTable
  const columns = useMemo<ColumnDef<ListingRatings>[]>(
    () => [
      {
        accessorKey: 'username',
        header: 'Username',
        cell: (info) => {
          const username = info.getValue<string>()
          if (username) return username

          const user = info.row.original.session?.user
          return user?.email_address ?? user?.mobile_number ?? user?.full_name ?? user?.id ?? '-'
        }
      },
      {
        accessorKey: 'app_rating',
        header: 'App Rating',
        cell: (info) => info.getValue()
      },
      {
        accessorKey: 'experience_rating',
        header: 'Experience Rating',
        cell: (info) => info.getValue()
      },
      {
        accessorKey: 'listing_rating',
        header: 'Listing Rating',
        cell: (info) => info.getValue()
      },
      {
        accessorKey: 'review',
        header: 'Review',
        cell: (info) => info.getValue()
      },
      {
        accessorKey: 'created_at',
        header: 'Created At',
        cell: (info) => {
          const datetime = info.getValue<DateTime>()
          return datetime ? datetime.toFormat('dd LLL yyyy hh:mm:ss a') : '-'
        }
      }
    ],
    []
  )

  // Define filter columns
  const filterColumns: FilterColumn[] = [
    {
      columnKey: 'username',
      header: 'Username',
      dataType: 'string'
    },
    {
      columnKey: 'app_rating',
      header: 'App Rating',
      dataType: 'number'
    },
    {
      columnKey: 'experience_rating',
      header: 'Experience Rating',
      dataType: 'number'
    },
    {
      columnKey: 'listing_rating',
      header: 'Listing Rating',
      dataType: 'number'
    },
    {
      columnKey: 'review',
      header: 'Review',
      dataType: 'string'
    }
  ]

  // Use the service URL based on whether we have a listing ID
  const serviceUrl = listingId
    ? ListingRatingService.getByListingId(listingId)
    : ListingRatingService.getAll

  return (
    <DataTable<ListingRatings, unknown, ListingRatingsResponse>
      columns={columns}
      filterColumns={filterColumns}
      swrService={serviceUrl}
      toRow={ListingRatingService.toRow}
      toPaginate={ListingRatingService.toPaginate}
      sortParam="sort"
      sortColumns={['username', 'app_rating', 'experience_rating', 'listing_rating', 'created_at']}
    />
  )
}

export default ListingRatingTable
