import { CreateListing, Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useContext } from 'react'
import { Content, Title } from '../common'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { Input } from '../ui/input'
import { Button } from '../ui/button'

const Search: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>Search</CardTitle>

          <FormSheet title="Edit Search" button="Edit Search" edit formId="listing-search">
            <SearchForm listing={listing} />
          </FormSheet>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Keywords</Title>
          <div>
            {listing?.keywords?.map((keyword) => (
              <Content key={keyword}>{keyword}</Content>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const SearchForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false,
    defaultValues: {
      keywords: listing?.keywords
    }
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        await ListingService.clientUpdate(listing.id, values)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-search">
        <FormField
          control={control}
          name="keywords"
          defaultValue={[]}
          rules={{ required: 'Please enter at least 1 keyword' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Keywords</FormLabel>
              <FormControl>
                <div className="space-y-1">
                  {field.value.map((keyword, index) => (
                    <Input
                      key={index}
                      {...field}
                      value={keyword}
                      onChange={(e) => {
                        const newKeyword = [...field.value]
                        newKeyword[index] = e.target.value
                        field.onChange(newKeyword)
                      }}
                    />
                  ))}
                  <Button
                    type="button"
                    onClick={() => {
                      const newKeyword = [...field.value, '']
                      field.onChange(newKeyword)
                    }}
                  >
                    Add Keyword
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default Search
