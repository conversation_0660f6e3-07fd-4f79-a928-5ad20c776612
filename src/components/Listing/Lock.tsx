import { CreateListing, Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useContext } from 'react'
import { Content, Title } from '../common'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { Input } from '../ui/input'
import { Checkbox } from '@/components/ui/checkbox'

const Lock: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>Lock</CardTitle>

          <FormSheet title="Edit Lock" button="Edit Lock" edit formId="listing-lock">
            <LockForm listing={listing} />
          </FormSheet>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Lock Id</Title>
          <Content>{listing?.lock_id}</Content>
          <Title>Lock Master Pin</Title>
          <Content>{listing?.lock_master_pin}</Content>
          <Title>Lock Bluetooth Admin Key</Title>
          <Content>{listing?.lock_bluetooth_admin_key}</Content>
          <Title>Door is lockable</Title>
          <Content>{listing?.door_is_lockable ? 'Lockable' : 'Unlockable'}</Content>
        </div>
      </CardContent>
    </Card>
  )
}

const LockForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false,
    defaultValues: {
      lock_id: listing?.lock_id,
      lock_master_pin: listing?.lock_master_pin,
      lock_bluetooth_admin_key: listing?.lock_bluetooth_admin_key,
      door_is_lockable: listing?.door_is_lockable
    }
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        await ListingService.clientUpdate(listing.id, values)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-lock">
        <FormField
          control={control}
          name="lock_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Lock Id</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="lock_master_pin"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Lock Master Pin</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="lock_bluetooth_admin_key"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Lock Bluetooth Admin Key</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="door_is_lockable"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Is the door lockable?</FormLabel>
              </div>
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default Lock
