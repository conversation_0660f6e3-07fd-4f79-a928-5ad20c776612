import { Listing, ListingStatus } from '@/types/Listing'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { cn, statusToColor } from '@/lib/utils'

const CurrentSession: FC<{ listing?: Listing }> = ({ listing }) => {
  if (listing?.sessions?.length == 0 || !listing) {
    return <>This Listing does not have any session yet.</>
  }

  const session = listing?.sessions?.[0]

  return (
    <div className="grid grid-cols-1 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div className="flex w-full justify-between">
            <CardTitle>Listing</CardTitle>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Listing Status</Title>
            <Content>
              <div className="flex justify-end space-x-2">
                <div
                  className={cn(
                    'h-1.5 w-1.5 self-center rounded-full',
                    statusToColor(listing.status == ListingStatus.idle ? 'approved' : 'rejected')
                  )}
                />
                <span className="capitalize">{listing.status.toUpperCase()}</span>
              </div>
            </Content>
            <Title>ID</Title>
            <Content>{listing?.id ?? '-'}</Content>
            <Title>Name</Title>
            <Content>{listing?.name ?? '-'}</Content>
            <Title>Address Name</Title>
            <Content>{listing?.address_name ?? '-'}</Content>
            <Title>Address</Title>
            <Content>{listing?.full_address ?? '-'}</Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div className="flex w-full justify-between">
            <CardTitle>
              {listing.sessions[0].actual_ended_at ? 'Last Session' : 'Current On Going Session'}
            </CardTitle>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Session Status</Title>
            <Content>
              <div className="flex justify-end space-x-2">
                <div
                  className={cn(
                    'h-1.5 w-1.5 self-center rounded-full',
                    statusToColor(listing.status == ListingStatus.idle ? 'rejected' : 'approved')
                  )}
                />
                <span className="capitalize">
                  {listing.sessions[0].actual_ended_at ? 'ENDED' : 'ON-GOING'}
                </span>
              </div>
            </Content>
            <Title>Session ID</Title>
            <Content>{session?.id ?? '-'}</Content>
            <Title>Username</Title>
            <Content>{session?.user.username ?? '-'}</Content>
            <Title>User Mobile Number</Title>
            <Content>{session?.user.full_mobile_number ?? '-'}</Content>
            <Title>User Email</Title>
            <Content>{session?.user.email_address ?? '-'}</Content>
            <Title>Started Date Time</Title>
            <Content>{session.started_at?.toFormat('yyyy-MM-dd hh:mm:ss a') ?? '-'}</Content>
            <Title>Expected End Date Time</Title>
            <Content>{session.expected_ended_at?.toFormat('yyyy-MM-dd hh:mm:ss a') ?? '-'}</Content>
            <Title>Actual End Date Time</Title>
            <Content>{session.actual_ended_at?.toFormat('yyyy-MM-dd hh:mm:ss a') ?? '-'}</Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div className="flex w-full justify-between">
            <CardTitle>Locker</CardTitle>
          </div>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Lock Bluetooth Guest Key</Title>
            <Content>
              <div className="break-words">{session?.lock_bluetooth_guest_key}</div>
            </Content>
            <Title>Last Custom Pin</Title>
            <Content>{session?.lock_custom_pin ?? '-'}</Content>
            <Title>Last Daily Pin</Title>
            <Content>{session?.lock_daily_pin}</Content>
            <Title>Last Hourly Pin</Title>
            <Content>{session?.lock_hourly_pin}</Content>
            <Title>Last One Time Pin</Title>
            <Content>{session?.lock_one_time_pin}</Content>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default CurrentSession
