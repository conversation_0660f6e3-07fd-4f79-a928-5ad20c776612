// import { mutate } from 'swr'
import { ChangeEvent, FC, useContext, useState } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
// import { serialize } from '@/network/request'
import { SheetContext } from '@/components/Form/FormSheet'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { mutate } from 'swr'

import { Label } from '../ui/label'
import { Card } from '../ui/card'
import { CreateListing } from '@/types/Listing'
import ListingService from '@/network/services/listing'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { DatePicker } from '../DatePicker/DatePicker'
import { RegionSearchBox } from '../Region/RegionSearchBox'
import { ActivitySearchBox } from '../Activity/ActivitySearchBox'
import { AmenitySearchBox } from '../Amenity/AmenitySearchBox'
import FileService from '@/network/services/file'
import { FileUpload } from '@/types/File'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '../ui/accordion'
import useAuth from '@/hooks/useAuth'

const ListingForm: FC = () => {
  const { user } = useAuth()
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const [imageType, setImageType] = useState('File')
  const [subImageType, setSubImageType] = useState('File')
  const [image, setImage] = useState('')
  const [file, setFile] = useState<File>()
  const [subImage, setSubImage] = useState<string[]>([])
  const [subFile, setSubFile] = useState<File[]>([])
  const [listingType, setListingType] = useState('')

  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false
  })

  const { handleSubmit, reset, control } = form

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setImage(URL.createObjectURL(e.target.files[0]))
      setFile(e.target.files[0])
    }
  }

  function handleSubChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setSubImage([...subImage, URL.createObjectURL(e.target.files[0])])
      setSubFile([...subFile, e.target.files[0]])
    }
  }

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (imageType == 'File' && file != undefined) {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('type', 'listing')

        const uploadFile = await FileService.clientUpload(formData as unknown as FileUpload)
        values['main_image_url'] = uploadFile.data.data.url
      }
      if (subImageType == 'File' && subFile.length > 0) {
        const uploadListFile: string[] = await Promise.all(
          subFile.map(async (image) => {
            const formData = new FormData()
            formData.append('file', image)
            formData.append('type', 'listing')

            const uploadFile = await FileService.clientUpload(formData as unknown as FileUpload)
            return uploadFile.data.data.url
          })
        )
        values['sub_image_urls'] = uploadListFile
      }

      // Add Admin info
      user?.id && (values['suggested_by'] = user?.id)

      await ListingService.clientCreate(values)

      toast({
        title: 'Success',
        description: 'Create Listing successfully.',
        action: <ToastAction altText="OK">OK</ToastAction>
      })
      drawerContext.setIsOpen(false)
      mutate((key) => typeof key === 'string' && key.startsWith(ListingService.getAll))

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-form">
        <Accordion type="multiple" defaultValue={['general-information']}>
          <AccordionItem value="general-information">
            <AccordionTrigger>General Information *</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <FormField
                  control={control}
                  name="listing_type"
                  rules={{ required: 'Please select a listing type' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Listing Type</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value)
                            setListingType(value)
                          }}
                          defaultValue={field?.value?.toString()}
                        >
                          <SelectTrigger className="mt-2 py-5">
                            <SelectValue placeholder="" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gomama">Gomama</SelectItem>
                            <SelectItem value="care">Care</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {listingType == 'gomama' && (
                  <FormField
                    control={control}
                    name="firestore_id"
                    rules={{ required: 'Please enter the name' }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Sensor Firestore ID</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="region"
                  rules={{ required: 'Please select a region' }}
                  render={({ field }) => {
                    return (
                      <RegionSearchBox
                        slug={field.value ? field.value.toString() : ''}
                        name={field.name}
                      />
                    )
                  }}
                />

                <FormField
                  control={control}
                  name="name"
                  rules={{ required: 'Please enter the name' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="company_name"
                  rules={{ required: 'Please enter the company name' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="address_name"
                  rules={{ required: 'Please enter the address name' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="description"
                  rules={{ required: 'Please enter the description' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="full_address"
                  rules={{ required: 'Please enter the full address' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Address</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="longitude"
                  rules={{ required: 'Please enter the longitude' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Longitude</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" min={-180} max={180} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="latitude"
                  rules={{ required: 'Please enter the latitude' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Latitude</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="contact_number"
                  rules={{ minLength: 2, maxLength: 18 }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Number</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="country_dial_code"
                  rules={{ required: 'Please enter the country dial code', maxLength: 3 }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country Dial Code</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="facility-details">
            <AccordionTrigger>Facility Details *</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <FormField
                  control={control}
                  name="keywords"
                  defaultValue={[]}
                  rules={{ required: 'Please enter at least 1 keyword' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Keywords</FormLabel>
                      <FormControl>
                        <div className="space-y-1">
                          {field.value.map((keyword, index) => (
                            <Input
                              key={index}
                              {...field}
                              value={keyword}
                              onChange={(e) => {
                                const newKeyword = [...field.value]
                                newKeyword[index] = e.target.value
                                field.onChange(newKeyword)
                              }}
                            />
                          ))}
                          <Button
                            type="button"
                            onClick={() => {
                              const newKeyword = [...field.value, '']
                              field.onChange(newKeyword)
                            }}
                          >
                            Add Keyword
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="postal_code"
                  rules={{ required: 'Please enter the postal code' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Code</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="diaper_changing_mat_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Diaper Changing Mat Type</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="opening_hours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Opening Hours</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="usage_durations"
                  defaultValue={[]}
                  rules={{ required: 'Please enter at least 1 duration' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Usage Durations</FormLabel>
                      <FormControl>
                        <div className="space-y-1">
                          {field.value.map((value, index) => (
                            <Input
                              key={index}
                              {...field}
                              value={value}
                              onChange={(e) => {
                                const newValue = [...field.value]
                                newValue[index] = parseInt(e.target.value)
                                field.onChange(newValue)
                              }}
                              type="number"
                            />
                          ))}
                          <Button
                            type="button"
                            onClick={() => {
                              const newValue = [...field.value, '']
                              field.onChange(newValue)
                            }}
                          >
                            Add Usage Duration
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="usage_extension_durations"
                  defaultValue={[]}
                  rules={{ required: 'Please enter at least 1 duration' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Usage Extension Durations</FormLabel>
                      <FormControl>
                        <div className="space-y-1">
                          {field.value.map((value, index) => (
                            <Input
                              key={index}
                              {...field}
                              value={value}
                              onChange={(e) => {
                                const newValue = [...field.value]
                                newValue[index] = parseInt(e.target.value)
                                field.onChange(newValue)
                              }}
                              type="number"
                            />
                          ))}
                          <Button
                            type="button"
                            onClick={() => {
                              const newValue = [...field.value, '']
                              field.onChange(newValue)
                            }}
                          >
                            Add Usage Extension Duration
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="max_number_of_usage_extensions"
                  rules={{ required: 'Please enter the max number of usage extensions' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Number Of Usage Extensions</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" min={1} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="number_of_private_feeding_rooms"
                  rules={{ required: 'Please enter the number of private feeding rooms' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number Of Private Feeding Rooms</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" min={1} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="number_of_diaper_changing_mats"
                  rules={{ required: 'Please enter the number of diaper changing mats' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number Of Diaper Changing Mats</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="activities-amenities">
            <AccordionTrigger>Activity & Amenity</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <FormField
                  control={control}
                  name="activities"
                  defaultValue={[]}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Activities</FormLabel>
                      <FormControl>
                        <div className="space-y-1">
                          {field.value.map((value, index) => (
                            <div key={value} className="relative">
                              <ActivitySearchBox
                                slug={value ? value.toString() : ''}
                                value={field.value}
                                onChange={field.onChange}
                                index={index}
                              />

                              <Button
                                className="absolute top-0  right-10 bg-transparent text-black border-none shadow-transparent hover:text-white"
                                onClick={() => {
                                  const newValue = [...field.value]
                                  newValue.splice(index, 1)
                                  field.onChange(newValue)
                                }}
                                type="button"
                              >
                                X
                              </Button>
                            </div>
                          ))}
                          <Button
                            type="button"
                            onClick={() => {
                              const newValue = [...field.value, '']
                              field.onChange(newValue)
                            }}
                          >
                            Add Activity
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="amenities"
                  defaultValue={[]}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amenities</FormLabel>
                      <FormControl>
                        <div className="space-y-1">
                          {field.value.map((value, index) => (
                            <div key={value} className="relative">
                              <AmenitySearchBox
                                key={index}
                                slug={value ? value.toString() : ''}
                                value={field.value}
                                onChange={field.onChange}
                                index={index}
                              />

                              <Button
                                className="absolute top-0  right-10 bg-transparent text-black border-none shadow-transparent hover:text-white"
                                onClick={() => {
                                  const newValue = [...field.value]
                                  newValue.splice(index, 1)
                                  field.onChange(newValue)
                                }}
                                type="button"
                              >
                                X
                              </Button>
                            </div>
                          ))}
                          <Button
                            type="button"
                            onClick={() => {
                              const newValue = [...field.value, '']
                              field.onChange(newValue)
                            }}
                          >
                            Add Amenity
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="image">
            <AccordionTrigger>Image</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                {' '}
                <div>
                  <FormLabel>Main Image Type</FormLabel>
                  <div className="flex gap-2">
                    <div className="flex gap-2">
                      <Input
                        type="radio"
                        id="file"
                        name="image_type"
                        value="File"
                        onChange={(e) => setImageType(e.target.value)}
                        className="h-6"
                        defaultChecked
                      />
                      <div className="flex h-full content-center flex-wrap">
                        <Label className="h-min">File</Label>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Input
                        type="radio"
                        id="url"
                        name="image_type"
                        value="URL"
                        onChange={(e) => setImageType(e.target.value)}
                        className="h-6"
                      />
                      <div className="flex h-full content-center flex-wrap">
                        <Label className="h-min">URL</Label>
                      </div>
                    </div>
                  </div>
                </div>
                {imageType == 'File' && (
                  <div className="mb-4">
                    <FormLabel>Listing Image</FormLabel>
                    <div className="w-48 h-80 relative mt-2">
                      <Card className="w-full h-full p-0  cursor-pointer">
                        {image != '' ? (
                          <img
                            src={image != '' ? image : ''}
                            alt="Main Image"
                            style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                          />
                        ) : (
                          <div
                            className="w-full h-full justify-center items-center absolute flex"
                            style={{ pointerEvents: 'none' }}
                          >
                            <div className="text-3xl">+</div>
                          </div>
                        )}
                        <input
                          type="file"
                          accept="image/*"
                          id="imageInput"
                          className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                          onChange={handleChange}
                        />
                      </Card>
                    </div>
                  </div>
                )}
                {imageType == 'URL' && (
                  <FormField
                    control={control}
                    name="main_image_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Image URL</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                {/* Sub Image Field */}
                <div>
                  <FormLabel>Sub Image Type</FormLabel>
                  <div className="flex gap-2">
                    <div className="flex gap-2">
                      <Input
                        type="radio"
                        id="file"
                        name="sub_image_type"
                        value="File"
                        onChange={(e) => setSubImageType(e.target.value)}
                        className="h-6"
                        defaultChecked
                      />
                      <div className="flex h-full content-center flex-wrap">
                        <Label className="h-min">File</Label>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Input
                        type="radio"
                        id="url"
                        name="sub_image_type"
                        value="URL"
                        onChange={(e) => setSubImageType(e.target.value)}
                        className="h-6"
                      />
                      <div className="flex h-full content-center flex-wrap">
                        <Label className="h-min">URL</Label>
                      </div>
                    </div>
                  </div>
                </div>
                {subImageType == 'File' && (
                  <div className="mb-4">
                    <FormLabel>Sub Image File</FormLabel>
                    <div className="flex space-x-4">
                      {subImage?.map((sub) => {
                        return (
                          <div className="w-48 h-80 relative mt-2" key={sub}>
                            <Card className="w-full h-full p-0  cursor-pointer">
                              <img
                                src={sub != '' ? sub : ''}
                                alt="Sub Image"
                                style={{
                                  objectFit: 'contain',
                                  pointerEvents: 'none',
                                  height: '100%'
                                }}
                              />
                            </Card>
                          </div>
                        )
                      })}

                      <div className="w-48 h-80 relative mt-2">
                        <Card className="w-full h-full p-0  cursor-pointer">
                          <div
                            className="w-full h-full justify-center items-center absolute flex"
                            style={{ pointerEvents: 'none' }}
                          >
                            <div className="text-3xl">+</div>
                          </div>

                          <input
                            type="file"
                            accept="image/*"
                            id="imageInput"
                            className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                            onChange={handleSubChange}
                          />
                        </Card>
                      </div>
                    </div>
                  </div>
                )}
                {subImageType == 'URL' && (
                  <FormField
                    control={control}
                    name="sub_image_urls"
                    defaultValue={[]}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Sub Image URLs</FormLabel>
                        <FormControl>
                          <div className="space-y-1">
                            {field.value.map((value, index) => (
                              <Input
                                key={index}
                                {...field}
                                value={value}
                                onChange={(e) => {
                                  const newValue = [...field.value]
                                  newValue[index] = e.target.value
                                  field.onChange(newValue)
                                }}
                              />
                            ))}
                            <Button
                              type="button"
                              onClick={() => {
                                const newValue = [...field.value, '']
                                field.onChange(newValue)
                              }}
                            >
                              Add Sub Image URL
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="optional">
            <AccordionTrigger>Optional Field</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <FormField
                  control={control}
                  name="note"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Note</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="api_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Api Key</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="pi_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pi Id</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="pi_last_updated"
                  render={({ field: { onChange, value } }) => {
                    const date = value ? new Date(value?.toLocaleString()) : new Date()
                    return (
                      <FormItem>
                        <FormLabel>PI Last Updated</FormLabel>
                        <FormControl>
                          <DatePicker onChange={onChange} value={date} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />

                <FormField
                  control={control}
                  name="lock_master_pin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lock Master Pin</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="lock_bluetooth_admin_key"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lock Bluetooth Admin Key</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* {listing && (
            <AccordionItem value="setting">
              <AccordionTrigger>Settings</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <FormField
                    control={control}
                    name="is_hidden"
                    render={({ field: { onChange, value } }) => (
                      <FormItem>
                        <FormControl>
                          <Checkbox label="Is Hidden" setChecked={onChange} checked={value} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="is_verified"
                    render={({ field: { onChange, value } }) => (
                      <FormItem>
                        <FormControl>
                          <Checkbox label="Is Verified" setChecked={onChange} checked={value} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="is_usage_extendable"
                    render={({ field: { onChange, value } }) => (
                      <FormItem>
                        <FormControl>
                          <Checkbox
                            label="Is Usage Extendable"
                            setChecked={onChange}
                            checked={value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="status"
                    rules={{ required: 'Please enter the role' }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field?.value?.toString()}
                          >
                            <SelectTrigger className="mt-2 py-5">
                              <SelectValue placeholder="" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="idle">Idle</SelectItem>
                              <SelectItem value="occupied">Occupied</SelectItem>
                              <SelectItem value="disinfecting">Super Admin</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="door_is_lockable"
                    render={({ field: { onChange, value } }) => (
                      <FormItem>
                        <FormControl>
                          <Checkbox
                            label="Door is Lockable"
                            setChecked={onChange}
                            checked={value}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="humidity"
                    rules={{ required: 'Please enter the humidity' }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Humidity</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="temperature"
                    rules={{ required: 'Please enter the temperature' }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Temperature</FormLabel>
                        <FormControl>
                          <Input {...field} type="number" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          )} */}
        </Accordion>
      </form>
    </Form>
  )
}

export default ListingForm
