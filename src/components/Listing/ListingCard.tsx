import { Listing } from '@/types/Listing'
import { FC } from 'react'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '../ui/tabs'
import GeneralInformation from './General'
import Suggestion from './Sugesstion'
import Location from './Location'
import Usage from './Usage'
import Search from './Search'
import Sensor from './Sensor'
import Media from './Media'
import Rating from './Rating'
import ListingAmenities from './ListingAmenities'
import ListingActivities from './ListingActivities'
import Hide from './Hide'
import CurrentSession from './CurrentSession'
import Lock from './Lock'

const ListingCard: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <>
      <Tabs
        defaultValue="listing"
        className="space-y-4"
        enableUrlHash
        tabValues={["listing", "rating", "amenities", "activities", "session"]}
      >
        <TabsList>
          <TabsTrigger value="listing">Listing</TabsTrigger>
          <TabsTrigger value="rating">Rating</TabsTrigger>
          <TabsTrigger value="amenities">Amenities</TabsTrigger>
          <TabsTrigger value="activities">Activities</TabsTrigger>
          <TabsTrigger value="session">Latest Session</TabsTrigger>
        </TabsList>

        <TabsContent value="listing">
          <div className="grid grid-cols-1 gap-4 xl:grid-cols-2">
            <GeneralInformation listing={listing} />
            <Location listing={listing} />
            <Suggestion listing={listing} />
            <Hide listing={listing} />
            <Usage listing={listing} />
            <Search listing={listing} />
            <Sensor listing={listing} />
            <Lock listing={listing} />
            <div className="xl:col-span-2">
              <Media listing={listing} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="rating">
          <div className="space-y-4">
            <Rating listing={listing} />
          </div>
        </TabsContent>

        <TabsContent value="amenities">
          <div className="space-y-4">
            <ListingAmenities listing={listing} />
          </div>
        </TabsContent>

        <TabsContent value="activities">
          <div className="space-y-4">
            <ListingActivities listing={listing} />
          </div>
        </TabsContent>

        <TabsContent value="session">
          <div className="space-y-4">
            <CurrentSession listing={listing} />
          </div>
        </TabsContent>
      </Tabs>
    </>
  )
}

export default ListingCard
