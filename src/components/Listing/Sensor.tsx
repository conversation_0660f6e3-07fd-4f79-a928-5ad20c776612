import { CreateListing, Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useContext } from 'react'
import { Content, Title } from '../common'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { Input } from '../ui/input'

const Sensor: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>Sensor</CardTitle>

          <FormSheet title="Edit Sensor" button="Edit Sensor" edit formId="listing-sensor">
            <SensorForm listing={listing} />
          </FormSheet>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Humidity</Title>
          <Content>{listing?.humidity}</Content>
          <Title>Temperature</Title>
          <Content>{listing?.temperature}</Content>
          <Title>Pi Id</Title>
          <Content>{listing?.pi_id}</Content>
          <Title>Pi Last Updated</Title>
          <Content>{listing?.pi_last_updated && listing?.pi_last_updated.toLocaleString()}</Content>
          <Title>Api key</Title>
          <Content>{listing?.api_key}</Content>
        </div>
      </CardContent>
    </Card>
  )
}

const SensorForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false,
    defaultValues: {
      pi_id: listing?.pi_id,
      api_key: listing?.api_key
    }
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        await ListingService.clientUpdate(listing.id, values)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-sensor">
        <FormField
          control={control}
          name="pi_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Pi Id</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="api_key"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Api Key</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default Sensor
