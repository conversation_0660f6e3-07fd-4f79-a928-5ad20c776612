import { Listing } from '@/types/Listing'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useState } from 'react'
import { Content, Title } from '../common'
import { Button } from '../ui/button'
import { Check } from 'lucide-react'
import ListingService from '@/network/services/listing'
import { toast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { ToastAction } from '../ui/toast'
import { cn, statusToColor } from '@/lib/utils'
import { Icons } from '../icons'

const Suggestion: FC<{ listing?: Listing }> = ({ listing }) => {
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>Suggestion</CardTitle>
          <div className="flex space-x-2">
            <Button
              disabled={listing?.is_verified || isSubmitting}
              onClick={async () => {
                setIsSubmitting(true)
                try {
                  if (listing) {
                    await ListingService.clientUpdate(listing.id, { is_verified: true })

                    toast({
                      title: 'Success',
                      description: 'Verified Listing successfully.',
                      action: <ToastAction altText="OK">OK</ToastAction>
                    })
                    mutate(
                      (key) =>
                        typeof key === 'string' &&
                        key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
                    )
                  }
                  setIsSubmitting(false)
                } catch (error) {
                  console.error(error)
                  toast({
                    title: 'Failed',
                    description: 'Something went wrong',
                    variant: 'destructive'
                  })
                  setIsSubmitting(false)
                }
              }}
            >
              {isSubmitting ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Check className="w-5 mr-2" />
              )}
              Approve
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Approve Status</Title>
          <Content>
            <div className="flex justify-end space-x-2">
              <div
                className={cn(
                  'h-1.5 w-1.5 self-center rounded-full',
                  statusToColor(listing?.is_verified ? 'approved' : 'rejected')
                )}
              />
              <span className="capitalize">{listing?.is_verified ? 'Approved' : 'Unapproved'}</span>
            </div>
          </Content>
          <Title>Approved/Verified By</Title>
          <Content>{listing?.verified_by ?? '-'}</Content>
          <Title>Suggested By</Title>
          <Content>{listing?.sugessted_by ?? '-'}</Content>
        </div>
      </CardContent>
    </Card>
  )
}

export default Suggestion
