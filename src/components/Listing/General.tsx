import { CreateListing, Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useContext } from 'react'
import { Content, Title } from '../common'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Input } from '../ui/input'
import { DrawerProps } from '../Form/schema'

const GeneralInformation: FC<{ listing?: Listing }> = ({ listing }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>General Information</CardTitle>

          <FormSheet
            title="Edit General Information"
            button="Edit General Information"
            edit
            formId="listing-general"
          >
            <GeneralForm listing={listing} />
          </FormSheet>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 gap-2">
          <Title>Name</Title>
          <Content>{listing?.name}</Content>
          <Title>Listing Type</Title>
          <Content>{listing?.listing_type}</Content>
          <Title>Company Name</Title>
          <Content>{listing?.company_name}</Content>
          <Title>Description</Title>
          <Content>{listing?.description}</Content>
          <Title>Contact Number</Title>
          <Content>{listing?.contact_number}</Content>
        </div>
      </CardContent>
    </Card>
  )
}

const GeneralForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false,
    defaultValues: {
      name: listing?.name,
      listing_type: listing?.listing_type,
      company_name: listing?.company_name,
      description: listing?.description,
      contact_number: listing?.contact_number
    }
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        await ListingService.clientUpdate(listing.id, values)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )
        drawerContext.setIsOpen(false)
        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-general">
        <FormField
          control={control}
          name="name"
          rules={{ required: 'Please enter the name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="listing_type"
          rules={{ required: 'Please select a listing type' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Listing Type</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field?.value?.toString()}>
                  <SelectTrigger className="mt-2 py-5">
                    <SelectValue placeholder="" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gomama">Gomama</SelectItem>
                    <SelectItem value="care">Care</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="company_name"
          rules={{ required: 'Please enter the company name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Company Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="description"
          rules={{ required: 'Please enter the description' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="contact_number"
          rules={{ minLength: 2, maxLength: 18 }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Contact Number</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default GeneralInformation
