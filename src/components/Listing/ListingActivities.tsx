import { CreateListing, Listing } from '@/types/Listing'
import { FC, useContext, useEffect } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { Button } from '../ui/button'
import { ActivitySearchBox } from '../Activity/ActivitySearchBox'
import { Activity } from '@/types/Activity'

const ListingActivities: FC<{ listing?: Listing }> = ({ listing }) => {
  const tableRow = [
    {
      accessorKey: 'name',
      header: 'Name'
    },
    {
      accessorKey: 'slug',
      header: 'Slug'
    },
    {
      accessorKey: 'description',
      header: 'Description'
    }
  ]

  return (
    <>
      <div className="flex w-full justify-end">
        <FormSheet
          title="Edit Activities"
          button="Edit Activities"
          edit
          formId="listing-activities"
        >
          <ActivitiesForm listing={listing} />
        </FormSheet>
      </div>
      <div className="rounded-md border max-w-[calc(100vw-32px)] lg:max-w-auto">
        <Table>
          <TableHeader>
            <TableRow>
              {tableRow?.map((header) => {
                return <TableHead key={header.header}>{header.header}</TableHead>
              })}
            </TableRow>
          </TableHeader>
          <TableBody>
            {listing?.activities?.length ?? 0 > 0 ? (
              <>
                {listing?.activities?.map((item) => {
                  return (
                    <TableRow key={item.id}>
                      {tableRow?.map((row) => {
                        const data = item[row.accessorKey as keyof Activity]
                        return (
                          <TableHead key={row.accessorKey}>
                            {typeof data !== 'object' ? data : ''}
                          </TableHead>
                        )
                      })}
                    </TableRow>
                  )
                })}
              </>
            ) : (
              <TableRow>
                <TableCell colSpan={tableRow.length} className="h-24 text-center">
                  No activities.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </>
  )
}

const ActivitiesForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false
  })

  const { handleSubmit, reset, setValue, control } = form

  useEffect(() => {
    if (listing) {
      if (listing.activities) {
        const activitiesId = listing.activities?.map((activity) => {
          return activity.slug
        })
        setValue('activities', activitiesId)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listing])

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        const activities = {
          activities: values['activities'].map((activity) => {
            return {
              activity_slug: activity,
              is_hidden: false
            }
          })
        }

        await ListingService.clientUpdateActivitiesAmenities(listing.id, activities)

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
        )

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-activities">
        <FormField
          control={control}
          name="activities"
          defaultValue={[]}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Activities</FormLabel>
              <FormControl>
                <div className="space-y-1">
                  {field.value.map((value, index) => (
                    <div key={value} className="relative">
                      <ActivitySearchBox
                        slug={value ? value.toString() : ''}
                        value={field.value}
                        onChange={field.onChange}
                        index={index}
                      />

                      <Button
                        className="absolute top-0  right-10 bg-transparent text-black border-none shadow-transparent hover:text-white"
                        onClick={() => {
                          const newValue = [...field.value]
                          newValue.splice(index, 1)
                          field.onChange(newValue)
                        }}
                        type="button"
                      >
                        X
                      </Button>
                    </div>
                  ))}
                  <Button
                    type="button"
                    onClick={() => {
                      const newValue = [...field.value, '']
                      field.onChange(newValue)
                    }}
                  >
                    Add Activity
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default ListingActivities
