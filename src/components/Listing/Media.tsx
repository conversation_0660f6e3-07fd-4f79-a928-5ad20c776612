import { CreateListing, Listing } from '@/types/Listing'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ChangeEvent, FC, useContext, useEffect, useState } from 'react'
import FormSheet, { SheetContext } from '../Form/FormSheet'
import { useForm } from 'react-hook-form'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form'
import { cn } from '@/lib/utils'
import ListingService from '@/network/services/listing'
import { ToastAction } from '@radix-ui/react-toast'
import { useToast } from '../ui/use-toast'
import { mutate } from 'swr'
import { serialize } from '@/network/request'
import { DrawerProps } from '../Form/schema'
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import FileService from '@/network/services/file'
import { FileUpload } from '@/types/File'
import { Label } from '../ui/label'
import { ListingFile } from '@/types/ListingFile'
import { Title } from '../common'

const Media: FC<{ listing?: Listing }> = ({ listing }) => {
  const mainImage = listing?.listing_files.find((files) => files.is_main == true)
  const subImages = listing?.listing_files.filter((files) => files.is_main != true)

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0">
        <div className="flex w-full justify-between">
          <CardTitle>Media</CardTitle>

          <FormSheet title="Edit Media" button="Edit Media" edit formId="listing-media">
            <MediaForm listing={listing} />
          </FormSheet>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid gap-2">
          <div className="flex">
            {listing?.listing_files?.length ?? 0 > 0 ? (
              <>
                <div className="space-y-4">
                  <Title>Main Image</Title>
                  <div className="w-48 h-80 relative">
                    <Card className="w-full h-full">
                      <img
                        src={mainImage?.image_url}
                        alt="Image"
                        style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                      />
                    </Card>
                  </div>

                  {subImages && subImages?.length > 0 ? (
                    <>
                      <Title>Sub Image</Title>
                      <div className="max-w-[100vw] lg:max-w-[calc(75vw-56px)] lg:max-w-auto overflow-x-scroll overflow-y-hidden max-w-fit">
                        <div className="flex space-x-4 justify-start w-fit">
                          {subImages?.map((image) => (
                            <div className="w-48 h-80 relative" key={image.id}>
                              <Card className="w-full h-full">
                                <img
                                  src={image?.image_url}
                                  alt="Image"
                                  style={{
                                    objectFit: 'contain',
                                    pointerEvents: 'none',
                                    height: '100%'
                                  }}
                                />
                              </Card>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  ) : (
                    <>No Sub Image</>
                  )}
                </div>
              </>
            ) : (
              <>No listing files.</>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const MediaForm: FC<{ listing?: Listing }> = ({ listing }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const [imageType, setImageType] = useState('File')
  const [subImageType, setSubImageType] = useState('File')
  const [image, setImage] = useState('')
  const [file, setFile] = useState<File>()
  const [subImage, setSubImage] = useState<ListingFile[]>([])
  const [newFile, setNewFile] = useState<File[]>([])
  const [deleteImage, setDeleteImage] = useState<string[]>([])
  const form = useForm<CreateListing>({
    shouldUseNativeValidation: false,
    defaultValues: {}
  })

  const { handleSubmit, reset, control, setValue } = form

  useEffect(() => {
    if (listing) {
      //Initialize Image data to form field
      if (listing?.listing_files?.length > 0) {
        const mainImage = listing?.listing_files.find((files) => files.is_main == true)
        setImage(mainImage?.image_url ?? '')
        setValue('main_image_url', mainImage?.image_url ?? '')

        const subImage = listing?.listing_files
          .filter((files) => files.is_main != true)
          .map((item) => item)
        setSubImage(subImage)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listing])

  useEffect(() => {
    //Refresh delete list when changing type to prevent multiple delete same image
    if (listing && listing?.listing_files?.length > 0) {
      const subImage = listing?.listing_files
        .filter((files) => files.is_main != true)
        .map((item) => item)
      setSubImage(subImage)

      setDeleteImage([])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subImageType])

  //Todo : handle change when image upload file
  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setImage(URL.createObjectURL(e.target.files[0]))
      setFile(e.target.files[0])
    }
  }

  function handleSubChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      // setSubImage([...subImage, URL.createObjectURL(e.target.files[0])])
      setNewFile([...newFile, e.target.files[0]])
    }
  }

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (listing) {
        //Main Image update
        const mainImage = listing?.listing_files.find((files) => files.is_main == true)
        if (imageType == 'File' && file != undefined && mainImage?.id) {
          const formData = new FormData()
          formData.append('file', file)
          formData.append('type', 'listing')

          const uploadFile = await FileService.clientUpload(formData as unknown as FileUpload)
          const newURL = uploadFile.data.data.url.toString()
          await ListingService.clientUpdateImage(mainImage?.id, { image_url: newURL })
        }

        if (imageType == 'URL' && mainImage?.id) {
          const newURL = values['main_image_url']
          await ListingService.clientUpdateImage(mainImage?.id, { image_url: newURL })
        }

        //SubImage Delete
        if (deleteImage.length > 0) {
          deleteImage.forEach(async (imageId) => {
            await ListingService.clientUpdateImage(imageId, { to_delete: true })
          })
        }

        //SubImage Add
        if (subImageType == 'File' && newFile.length > 0) {
          const uploadListFile: string[] = await Promise.all(
            newFile.map(async (image) => {
              const formData = new FormData()
              formData.append('file', image)
              formData.append('type', 'listing')

              const uploadFile = await FileService.clientUpload(formData as unknown as FileUpload)
              return uploadFile.data.data.url
            })
          )
          await ListingService.clientUpdate(listing.id, { sub_image_urls: uploadListFile })
        }

        if (subImageType == 'URL') {
          const newImageArray = values['sub_image_urls']
          await ListingService.clientUpdate(listing.id, { sub_image_urls: newImageArray })
        }

        toast({
          title: 'Success',
          description: 'Update Listing successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        setTimeout(() => {
          mutate(
            (key) =>
              typeof key === 'string' &&
              key.startsWith(serialize(ListingService.getSingle(listing.id), {}))
          )
        }, 500) // Delay in milliseconds

        reset()
      }
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="listing-media">
        {/* Main Image Field */}
        <div>
          <FormLabel>Main Image Type</FormLabel>
          <div className="flex gap-2">
            <div className="flex gap-2">
              <Input
                type="radio"
                id="file"
                name="image_type"
                value="File"
                onChange={(e) => setImageType(e.target.value)}
                className="h-6"
                defaultChecked
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">File</Label>
              </div>
            </div>

            <div className="flex gap-2">
              <Input
                type="radio"
                id="url"
                name="image_type"
                value="URL"
                onChange={(e) => setImageType(e.target.value)}
                className="h-6"
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">URL</Label>
              </div>
            </div>
          </div>
        </div>

        {imageType == 'File' && (
          <div className="mb-4">
            <FormLabel>Listing Image</FormLabel>
            <div className="w-48 h-80 relative mt-2">
              <Card className="w-full h-full p-0  cursor-pointer">
                {image != null ? (
                  <img
                    src={image ?? ''}
                    alt="Main Image"
                    style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                  />
                ) : (
                  <div
                    className="w-full h-full justify-center items-center absolute flex"
                    style={{ pointerEvents: 'none' }}
                  >
                    <div className="text-3xl">+</div>
                  </div>
                )}
                <input
                  type="file"
                  accept="image/*"
                  id="imageInput"
                  className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                  onChange={handleChange}
                />
              </Card>
            </div>
          </div>
        )}

        {imageType == 'URL' && (
          <FormField
            control={control}
            name="main_image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Image URL</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* Sub Image Field */}
        <div>
          <FormLabel>Sub Image Type</FormLabel>
          <div className="flex gap-2">
            <div className="flex gap-2">
              <Input
                type="radio"
                id="file"
                name="sub_image_type"
                value="File"
                onChange={(e) => setSubImageType(e.target.value)}
                className="h-6"
                defaultChecked
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">File</Label>
              </div>
            </div>

            <div className="flex gap-2">
              <Input
                type="radio"
                id="url"
                name="sub_image_type"
                value="URL"
                onChange={(e) => setSubImageType(e.target.value)}
                className="h-6"
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">URL</Label>
              </div>
            </div>
          </div>
        </div>

        {subImageType == 'File' && (
          <div className="mb-4">
            <FormLabel>Sub Image File</FormLabel>
            <div className=" lg:max-w-auto overflow-x-scroll overflow-y-hidden">
              <div className="flex space-x-4 justify-start w-fit ">
                {subImage?.map((sub, index) => {
                  return (
                    <div className="w-48 h-80 relative mt-2" key={sub.id}>
                      <Card className="w-full h-full p-0 relative ">
                        <img
                          src={sub?.image_url ?? ''}
                          alt="Sub Image"
                          style={{
                            objectFit: 'contain',
                            pointerEvents: 'none',
                            height: '100%'
                          }}
                        />

                        <Button
                          className="absolute top-0 right-0 bg-transparent text-black border-none shadow-transparent hover:text-white"
                          onClick={() => {
                            deleteImage.push(sub.id)
                            const imageArray = [...subImage]
                            imageArray.splice(index, 1)
                            setSubImage(imageArray)
                          }}
                          type="button"
                        >
                          X
                        </Button>
                      </Card>
                    </div>
                  )
                })}

                {newFile?.map((image, index) => (
                  <div className="w-48 h-80 relative mt-2" key={image.name}>
                    <Card className="w-full h-full p-0 relative ">
                      <img
                        src={URL.createObjectURL(image)}
                        alt="New Image"
                        style={{
                          objectFit: 'contain',
                          pointerEvents: 'none',
                          height: '100%'
                        }}
                      />

                      <Button
                        className="absolute top-0 right-0 bg-transparent text-black border-none shadow-transparent hover:text-white"
                        onClick={() => {
                          const imageArray = [...newFile]
                          imageArray.splice(index, 1)
                          setNewFile(imageArray)
                        }}
                        type="button"
                      >
                        X
                      </Button>
                    </Card>
                  </div>
                ))}

                <div className="w-48 h-80 relative mt-2">
                  <Card className="w-full h-full p-0  cursor-pointer">
                    <div
                      className="w-full h-full justify-center items-center absolute flex"
                      style={{ pointerEvents: 'none' }}
                    >
                      <div className="text-3xl">+</div>
                    </div>

                    <input
                      type="file"
                      accept="image/*"
                      id="imageInput"
                      className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                      onChange={handleSubChange}
                    />
                  </Card>
                </div>
              </div>
            </div>
          </div>
        )}

        {subImageType == 'URL' && (
          <FormField
            control={control}
            name="sub_image_urls"
            defaultValue={[]}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sub Image URLs</FormLabel>
                <FormControl>
                  <div className="space-y-1">
                    {subImage?.map((image, index) => (
                      <div className="relative">
                        <Input
                          key={image.id}
                          value={image.image_url}
                          readOnly
                          className="opacity-50"
                        />

                        <Button
                          className="absolute top-[50%] -translate-y-[50%] right-0 bg-transparent text-black border-none shadow-transparent hover:text-white"
                          onClick={() => {
                            deleteImage.push(image.id)
                            const imageArray = [...subImage]
                            imageArray.splice(index, 1)
                            setSubImage(imageArray)
                          }}
                          type="button"
                        >
                          X
                        </Button>
                      </div>
                    ))}
                    {field.value.map((value, index) => (
                      <div className="relative">
                        <Input
                          key={index}
                          {...field}
                          value={value}
                          onChange={(e) => {
                            const newValue = [...field.value]
                            newValue[index] = e.target.value
                            field.onChange(newValue)
                          }}
                        />

                        <Button
                          className="absolute top-[50%] -translate-y-[50%] right-0 bg-transparent text-black border-none shadow-transparent hover:text-white"
                          onClick={() => {
                            const newValue = [...field.value]
                            newValue.splice(index, 1)
                            field.onChange(newValue)
                          }}
                          type="button"
                        >
                          X
                        </Button>
                      </div>
                    ))}
                    <Button
                      type="button"
                      onClick={() => {
                        const newValue = [...field.value, '']
                        field.onChange(newValue)
                      }}
                    >
                      Add Sub Image URL
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </form>
    </Form>
  )
}

export default Media
