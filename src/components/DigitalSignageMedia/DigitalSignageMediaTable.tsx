import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { DateTime } from 'luxon'
import { DigitalSignageMedia, DigitalSignageMediaResponse } from '@/types/DigitalSignageMedia'
import DigitalSignageMediaService from '@/network/services/digitalSignageMedia'

const columns: ColumnDef<DigitalSignageMedia>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'created_by',
    header: 'Created By'
  },
  {
    accessorKey: 'updated_by',
    header: 'Updated By',
    cell: (props) => {
      return props.getValue<String>() ?? '-'
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'created_by',
    header: 'Created By',
    dataType: 'string'
  },
  {
    columnKey: 'updated_by',
    header: 'Updated By',
    dataType: 'string'
  }
]

const DigitalSignageMediaTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<DigitalSignageMedia, unknown, DigitalSignageMediaResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={DigitalSignageMediaService.getAll}
        toRow={DigitalSignageMediaService.toRow}
        toPaginate={DigitalSignageMediaService.toPaginate}
        onRowClick={(row: Row<DigitalSignageMedia>) => {
          nav(`/digital-signage-medias/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={['id', 'created_by', 'updated_by', 'created_at', 'updated_at']}
      />
    </>
  )
}

export default DigitalSignageMediaTable
