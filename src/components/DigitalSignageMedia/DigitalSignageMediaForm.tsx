import { FC, useContext, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import {
  CreateDigitalSignageMedia,
  DigitalSignageMedia,
  MediaType
} from '@/types/DigitalSignageMedia'
import DigitalSignageMediaService from '@/network/services/digitalSignageMedia'
import { CircleSpinner } from '../LoadingSpinner/circleSpinner'

const DigitalSignageMediaForm: FC<{ media?: DigitalSignageMedia }> = ({ media }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<CreateDigitalSignageMedia>({
    shouldUseNativeValidation: false,
    defaultValues: media
  })
  const { handleSubmit, reset, control } = form
  const [mediaPreview, setMediaPreview] = useState<string>(media?.media_url ?? '')
  const [mediaType, setMediaType] = useState<string>(media?.type ?? '')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchText = async () => {
      if (media?.media_url && media?.type == MediaType.text) {
        try {
          const response = await fetch(media?.media_url)
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`)
          }
          const data = await response.text()
          setMediaPreview(data)
        } catch (err) {
          throw new Error(`HTTP error! error: ${err}`)
        } finally {
          setLoading(false)
        }
      } else {
        setLoading(false)
      }
    }
    fetchText()
  }, [media?.media_url])

  const handleFileChange = (e: File) => {
    const file = e
    if (file) {
      const fileType = file.type.split('/')[0]

      switch (fileType) {
        case 'image':
          setMediaPreview(URL.createObjectURL(file))
          setMediaType(MediaType.image)
          break
        case 'video':
          setMediaPreview(URL.createObjectURL(file))
          setMediaType(MediaType.video)
          break
        case 'text':
          const reader = new FileReader()
          reader.onload = async (event) => {
            setMediaPreview(event.target?.result as string)
            setMediaType(MediaType.text)
          }
          reader.readAsText(file)
          break
        default:
          setMediaPreview('')
          setMediaType('')
      }
    }
  }

  const renderMediaPreview = () => {
    switch (mediaType) {
      case 'image':
        return <img src={mediaPreview} alt="Image Preview" className="max-w-full h-auto max-h-64" />
      case 'video':
        return <video src={mediaPreview} controls className="max-w-full max-h-64" />
      case 'text':
        return (
          <pre className="bg-gray-100 p-2 rounded overflow-auto min-w-full max-w-xs h-48">
            {mediaPreview}
          </pre>
        )
      default:
        return null
    }
  }

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!media) {
        console.log(values.media_file)
        await DigitalSignageMediaService.clientCreate({
          type: mediaType as MediaType,
          media_file: values.media_file,
          name: values.name
        })

        toast({
          title: 'Success',
          description: 'Create media successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) => typeof key === 'string' && key.startsWith(DigitalSignageMediaService.getAll)
        )
      } else {
        await DigitalSignageMediaService.clientUpdate(media.id, values)

        toast({
          title: 'Success',
          description: 'Update media successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(DigitalSignageMediaService.getSingle(media.id), {}))
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="digital-signage-media">
        <FormField
          control={control}
          name="name"
          rules={media ? {} : { required: 'Please enter the media name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Media Name {mediaType}</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="media_file"
          rules={media ? {} : { required: 'Please provide the digital signage image' }}
          render={({ field }) => (
            <FormItem className="flex flex-col col-span-2">
              <FormLabel>Media File</FormLabel>
              <div className="flex w-full space-x-2 ">
                <div className="flex-1">
                  {loading ? (
                    <div className="flex justify-center items-center h-full">
                      <CircleSpinner />
                    </div>
                  ) : (
                    mediaPreview && renderMediaPreview()
                  )}
                  <FormControl>
                    <Input
                      type="file"
                      onChange={(e) => {
                        if (e.target.files) {
                          field.onChange(e.target.files[0])
                          handleFileChange(e.target.files[0])
                          setMediaPreview(URL.createObjectURL(e.target.files[0]))
                        }
                      }}
                    />
                  </FormControl>
                </div>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default DigitalSignageMediaForm
