import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { DigitalSignageMedia, MediaType } from '@/types/DigitalSignageMedia'
import { Separator } from '../ui/separator'
import { DateTime } from 'luxon'

const DigitalSignageMediaCard: FC<{ media?: DigitalSignageMedia }> = ({ media }) => {
  let mediaContent = <></>

  switch (media?.type) {
    case MediaType.image:
      mediaContent = (
        <img className="w-[250px] float-right" src={media.media_url} alt="event-info-image" />
      )
      break
    case MediaType.text:
      mediaContent = <embed className="w-[250px] float-right" src={media.media_url} />
      break
    case MediaType.video:
      mediaContent = (
        <iframe
          className="w-[550px] h-[350px] float-right"
          src={media.media_url}
          title="YouTube video player"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        ></iframe>
      )
      break
  }

  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{media?.id}</Content>
            <Title>Media Name</Title>
            <Content>{media?.name}</Content>
            <Title>Media URL</Title>
            <Content>{media?.media_url}</Content>
            <Title>Media</Title>
            <Content>{mediaContent}</Content>
            <Title>Media Type</Title>
            <Content>{media?.type}</Content>
            <Title>Updated By</Title>
            <Content>{media?.updated_by ?? '-'}</Content>
            <Title>Created By</Title>
            <Content>{media?.created_by}</Content>
            <Title>Created At</Title>
            <Content>
              {media?.created_at ? media.created_at.toLocaleString(DateTime.DATETIME_SHORT) : '-'}
            </Content>
            <Title>Updated At</Title>
            <Content>
              {media?.updated_at ? media.updated_at.toLocaleString(DateTime.DATETIME_SHORT) : '-'}
            </Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Device Settings Linked to this Media</CardTitle>
        </CardHeader>
        <CardContent>
          {media?.device_settings.map((setting, index) => {
            return (
              <div className="grid grid-cols-2 gap-2 pt-10">
                <Title>ID</Title>
                <Content>{setting?.id}</Content>
                <Title>Name</Title>
                <Content>{setting?.name}</Content>
                {media.device_settings.length > 1 && index + 1 != media.device_settings.length && (
                  <Separator />
                )}
              </div>
            )
          })}
        </CardContent>
      </Card>
    </>
  )
}

export default DigitalSignageMediaCard
