import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import ListingFileService from '@/network/services/listingFile'
import { ListingFile } from '@/types/ListingFile'
import { useSearchListingFile } from '@/hooks/search/useSearchListingFile'

// multiple select
export const ListingFileSearchBox: FC<{ id: string; name: string }> = ({ id, name }) => {
  const [open, setOpen] = useState(false)
  const [hasValue, setHasValue] = useState(false)
  const [listingFile, setListingFile] = useState<ListingFile>()
  const form = useFormContext()

  useEffect(() => {
    const fetchListingFile = async () => {
      const listingFileData = await ListingFileService.clientGetSingle(id)

      if (listingFileData.data.data) {
        setListingFile(listingFileData.data.data)
      }
    }

    if (id) {
      fetchListingFile()
    }
  }, [id])

  useEffect(() => {
    if (id) {
      setHasValue(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleSetActive = (listingFile: ListingFile) => {
    setListingFile(listingFile)
    form.setValue(name, listingFile.id)
  }

  const displayName = !isEmpty(listingFile) ? listingFile?.id : 'Choose a listing file'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Main Image File</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(listingFile) && 'text-muted-foreground',
                'w-full'
              )}
              disabled={hasValue}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<ListingFile, ListingFile>
            fn={useSearchListingFile}
            renderFn={(prod: ListingFile) => prod.id}
            valueFn={(prod: ListingFile) => prod.id.toString()}
            compareFn={(prod: ListingFile) => {
              if (isEmpty(listingFile)) {
                return false
              }

              const findIndex = listingFile?.id == prod.id

              return findIndex
            }}
            selectedResult={listingFile}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
