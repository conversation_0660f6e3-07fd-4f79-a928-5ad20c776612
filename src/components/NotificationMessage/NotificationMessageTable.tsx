import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { DateTime } from 'luxon'
import { NotificationMessage, NotificationMessageResponse } from '@/types/NotificationMessage'
import NotificationMessageService from '@/network/services/notificationMessage'
import { User } from '@/types/User'

const columns: ColumnDef<NotificationMessage>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'title',
    header: 'Title'
  },
  {
    accessorKey: 'message',
    header: 'Message'
  },
  {
    accessorKey: 'create_user',
    header: 'Created By',
    cell: (props) => {
      const value = props.getValue<User>()
      return value ? value.id : '-'
    }
  },
  {
    accessorKey: 'update_user',
    header: 'Updated By',
    cell: (props) => {
      const value = props.getValue<User>()
      return value ? value.id : '-'
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'title',
    header: 'Title',
    dataType: 'string'
  }
]

const NotificationMessageTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<NotificationMessage, unknown, NotificationMessageResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={NotificationMessageService.getNotificationMessages}
        toRow={NotificationMessageService.toRow}
        toPaginate={NotificationMessageService.toPaginate}
        onRowClick={(row: Row<NotificationMessage>) => {
          nav(`/notification-messages/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={[
          'id',
          'title',
          'message',
          'create_user',
          'update_user',
          'created_at',
          'updated_at'
        ]}
      />
    </>
  )
}

export default NotificationMessageTable
