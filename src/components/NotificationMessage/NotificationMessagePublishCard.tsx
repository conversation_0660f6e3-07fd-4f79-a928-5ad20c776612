import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { FC, useRef, useState } from 'react'
import { NotificationMessage } from '@/types/NotificationMessage'
import NotificationMessageUserSelectTable from './NotificationMessageUserSelectTable'
import { Button } from '../ui/button'
import { MegaphoneIcon, SendIcon, UserCheck, UserPlus } from 'lucide-react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '../ui/alert-dialog'
import { CreatePublishedMessageInput, SendGroup } from '@/types/PublishedMessage'
import PublishedMessageService from '@/network/services/publishMessage'
import { useToast } from '../ui/use-toast'
import { ToastAction } from '../ui/toast'

const NotificationMessagePublishCard: FC<{ message: NotificationMessage }> = (
  notificationMessage
) => {
  const { toast } = useToast()
  const [isSendDialogOpen, setIsSendDialogOpen] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [selectedGroups, setSelectedGroups] = useState<SendGroup[]>([])

  const notificationMessagePublishCardRef = useRef<any>(null)

  const onResetRows = () => {
    if (notificationMessagePublishCardRef.current) {
      notificationMessagePublishCardRef.current.resetSelection()
    }
  }

  const groups: Array<{ label: string; value: SendGroup; description: string }> = [
    {
      label: 'All Users',
      value: SendGroup.ALL_USERS,
      description: `Send to the devices of all users available`
    },
    {
      label: 'New Registered',
      value: SendGroup.NEW_REGISTERED,
      description: `Send to the devices of all new registered users' past 30 days`
    },
    {
      label: 'Active Users',
      value: SendGroup.ACTIVE_30,
      description: `Send to the devices of all active users from the past 30 days. Users who logged in during the last 30 days are considered active`
    }
  ]

  const getSelectedUsers = (rowSelection: any) => {
    const selectedUserIds = Object.keys(rowSelection)
    setSelectedUsers(selectedUserIds)
  }
  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div className="flex w-full justify-between">
            <CardTitle>Publish Message</CardTitle>
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8"
                onClick={() => setIsSendDialogOpen(true)}
              >
                <SendIcon className="mr-2 h-4 w-4" />
                Publish
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="grid grid-cols-1 space-y-2">
          <div className="flex w-full justify-stretch space-x-2">
            {groups.map((group) => (
              <Card
                className={
                  'flex-1 cursor-pointer' +
                  (selectedGroups.includes(SendGroup.ALL_USERS) &&
                  group.value != SendGroup.ALL_USERS
                    ? ' cursor-not-allowed text-muted-foreground'
                    : '') +
                  (selectedGroups.includes(group.value) ? ' bg-slate-300' : '')
                }
                onClick={() => {
                  if (selectedGroups.includes(group.value)) {
                    const temp = [...selectedGroups]
                    const indexOf = temp.indexOf(group.value)
                    temp.splice(indexOf, 1)
                    setSelectedGroups(temp)
                  } else {
                    if (selectedGroups.includes(SendGroup.ALL_USERS)) {
                      return
                    }

                    if (group.value == SendGroup.ALL_USERS) {
                      onResetRows()
                      setSelectedGroups([SendGroup.ALL_USERS])
                      return
                    }

                    const temp = [...selectedGroups]
                    temp.push(group.value)
                    setSelectedGroups(temp)
                  }
                }}
              >
                <CardHeader>
                  <CardTitle className="flex space-x-3">
                    <div className="self-center">{group.label}</div>{' '}
                    {group.value == SendGroup.ALL_USERS && <MegaphoneIcon className=" h-6 w-6" />}{' '}
                    {group.value == SendGroup.NEW_REGISTERED && <UserPlus className=" h-6 w-6" />}
                    {group.value == SendGroup.ACTIVE_30 && <UserCheck className=" h-6 w-6" />}
                  </CardTitle>
                  <CardDescription>{group.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
          <NotificationMessageUserSelectTable
            getSelectedUsers={getSelectedUsers}
            disableSelect={selectedGroups.includes(SendGroup.ALL_USERS)}
            parentRef={notificationMessagePublishCardRef}
          />
        </CardContent>
      </Card>
      {/* Alert Dialogs */}
      <AlertDialog open={isSendDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm to publish the message?</AlertDialogTitle>
            <AlertDialogDescription>
              Once you published the message with title "{notificationMessage.message.title}", all
              targeted user(s) will receive the message, action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setIsSendDialogOpen(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={async () => {
                if (selectedUsers.length == 0 && selectedGroups.length == 0) {
                  toast({
                    title: 'Failed',
                    description: 'Please select atleast one user/group to publish the messsage',
                    variant: 'destructive'
                  })

                  return
                }

                const input: CreatePublishedMessageInput = {
                  users_selected: selectedUsers,
                  message_chosen: notificationMessage.message.id
                }

                try {
                  const response = await PublishedMessageService.clientCreate(input, selectedGroups)

                  toast({
                    title: 'Success',
                    description: `Published message to selected users' devices successfully.
                    Success: ${response.data.success_count}, Fail: ${response.data.fail_count}`,
                    action: <ToastAction altText="OK">OK</ToastAction>
                  })
                  setIsSendDialogOpen(false)
                } catch (err) {
                  toast({
                    title: 'Failed',
                    description: 'Something went wrong',
                    variant: 'destructive'
                  })
                  return
                }
              }}
            >
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default NotificationMessagePublishCard
