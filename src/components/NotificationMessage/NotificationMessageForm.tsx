import { FC, useContext, useState } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import { CreateNotificationMessageInput, NotificationMessage } from '@/types/NotificationMessage'
import NotificationMessageService from '@/network/services/notificationMessage'
import { Label } from '../ui/label'

const NotificationMessageForm: FC<{ notificationMessage?: NotificationMessage }> = ({
  notificationMessage
}) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const [imageType, setImageType] = useState('FILE')
  const [imagePreview, setImagePreview] = useState<string>(notificationMessage?.image_url ?? '')

  const form = useForm<CreateNotificationMessageInput>({
    shouldUseNativeValidation: false,
    defaultValues: notificationMessage
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!notificationMessage) {
        if (imageType == 'FILE') {
          delete values['image_url']
        } else if (imageType == 'URL') {
          delete values['image_file']
        }

        await NotificationMessageService.clientCreate({ ...values })

        toast({
          title: 'Success',
          description: 'Create Notification Message successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(NotificationMessageService.getNotificationMessages)
        )
      } else {
        if (imageType == 'FILE') {
          delete values['image_url']
        } else if (imageType == 'URL') {
          delete values['image_file']
        }

        await NotificationMessageService.clientUpdate(notificationMessage.id, values)

        toast({
          title: 'Success',
          description: 'Update Notification Message successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(
              serialize(
                NotificationMessageService.getNotificationMessage(notificationMessage.id),
                {}
              )
            )
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="notification-message">
        <FormField
          control={control}
          name="title"
          rules={{ required: 'Please enter title' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="message"
          rules={{ required: 'Please enter message' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Message</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <FormLabel>Main Image Type</FormLabel>
          <div className="flex gap-2">
            <div className="flex gap-2">
              <Input
                type="radio"
                id="file"
                name="image_type"
                value="FILE"
                onChange={(e) => setImageType(e.target.value)}
                className="h-6"
                defaultChecked
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">File</Label>
              </div>
            </div>

            <div className="flex gap-2">
              <Input
                type="radio"
                id="url"
                name="image_type"
                value="URL"
                onChange={(e) => setImageType(e.target.value)}
                className="h-6"
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">URL</Label>
              </div>
            </div>
          </div>
        </div>

        {imageType == 'URL' && (
          <FormField
            control={form.control}
            name="image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Image Url (Paste the full link instead of manual typing)</FormLabel>
                <div className="flex w-full space-x-2 ">
                  <div className="flex-1">
                    {imagePreview && (
                      <img
                        className="w-[180px] py-4"
                        src={imagePreview}
                        alt="notification-message-image"
                      />
                    )}
                    <FormControl>
                      <Input
                        {...field}
                        readOnly // Disable manual typing
                        onPaste={(e) => {
                          const pastedValue = e.clipboardData.getData('text')
                          field.onChange(pastedValue)
                          // Optionally, you can also update the image preview here
                          setImagePreview(pastedValue)
                        }}
                      />
                    </FormControl>
                  </div>
                </div>

                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {imageType == 'FILE' && (
          <FormField
            control={form.control}
            name="image_file"
            render={({ field }) => (
              <FormItem className="flex flex-col col-span-2">
                <FormLabel>Image File</FormLabel>
                <div className="flex w-full space-x-2 ">
                  <div className="flex-1">
                    {imagePreview && (
                      <img
                        className="w-[180px] py-4"
                        src={imagePreview}
                        alt="notification-message-image"
                      />
                    )}
                    <FormControl>
                      <Input
                        type="file"
                        onChange={(e) => {
                          if (e.target.files) {
                            field.onChange(e.target.files[0])
                            setImagePreview(URL.createObjectURL(e.target.files[0]))
                          }
                        }}
                      />
                    </FormControl>
                  </div>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </form>
    </Form>
  )
}

export default NotificationMessageForm
