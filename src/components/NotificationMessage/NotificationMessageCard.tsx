import { Card, CardContent } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { NotificationMessage } from '@/types/NotificationMessage'
import { DateTime } from 'luxon'

const NotificationMessageCard: FC<{ notificationMessage?: NotificationMessage }> = ({
  notificationMessage
}) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{notificationMessage?.id}</Content>
            <Title>Title</Title>
            <Content>{notificationMessage?.title}</Content>
            <Title>Message</Title>
            <Content>{notificationMessage?.message}</Content>
            <Title>Image</Title>
            <Content>
              {notificationMessage?.image_url ? (
                <img
                  className="justify-self-end"
                  src={notificationMessage?.image_url}
                  width={200}
                  height={200}
                />
              ) : (
                '-'
              )}
            </Content>
            <Title>Created At</Title>
            <Content>
              {notificationMessage
                ? notificationMessage.created_at.toLocaleString(DateTime.DATETIME_SHORT)
                : '-'}
            </Content>
            <Title>Created By</Title>
            <Content>{notificationMessage?.create_user.id ?? '-'}</Content>
            <Title>Updated At</Title>
            <Content>
              {notificationMessage
                ? notificationMessage.updated_at.toLocaleString(DateTime.DATETIME_SHORT)
                : '-'}
            </Content>
            <Title>Updated By</Title>
            <Content>{notificationMessage?.update_user?.id ?? '-'}</Content>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default NotificationMessageCard
