import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Session } from '@/types/Session'
import { FC } from 'react'
import FormSheet from '../Form/FormSheet'
import MiscForm from './MiscForm'
import { Content, Title } from '@/components/common'
import { durationFormatter } from '@/lib/utils'
import { Button } from '../ui/button'
import { Link } from 'react-router-dom'

const SessionCard: FC<{ session: Session }> = ({ session }) => {
  if (!session) {
    return <></>
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>Listing</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Lisiting Id</Title>
            <Content>{session?.listing_id}</Content>
            <Title>Listing Name</Title>
            <Content>{session?.listing?.name}</Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>Lock</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Lock Bluetooth Guest Key</Title>
            <Content>
              <div className="break-words">{session?.lock_bluetooth_guest_key}</div>
            </Content>
            <Title>Lock Custom Pin</Title>
            <Content>{session?.lock_custom_pin}</Content>
            <Title>Lock Daily Pin</Title>
            <Content>{session?.lock_daily_pin}</Content>
            <Title>Lock Hourly Pin</Title>
            <Content>{session?.lock_hourly_pin}</Content>
            <Title>Lock One Time Pin</Title>
            <Content>{session?.lock_one_time_pin}</Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>Actual Usage Duration</Title>
            <Content>
              {session?.actual_ended_at && session?.started_at
                ? durationFormatter(
                    session?.actual_ended_at.diff(session?.started_at, 'seconds').toObject()
                      .seconds ?? 0
                  )
                : '-'}
            </Content>
            <Title>Expected Usage Duration</Title>
            <Content>
              {session?.expected_ended_at && session?.started_at
                ? durationFormatter(
                    session?.expected_ended_at.diff(session?.started_at, 'seconds').toObject()
                      .seconds ?? 0
                  )
                : '-'}
            </Content>
            <Title>Started At</Title>
            <Content>{session?.started_at?.toFormat('yyyy LLL dd hh:mm:ss a')}</Content>
            <Title>Expected Ended At</Title>
            <Content>{session?.expected_ended_at?.toFormat('yyyy LLL dd hh:mm:ss a')}</Content>
            <Title>Actual Ended At</Title>
            <Content>{session?.actual_ended_at?.toFormat('yyyy LLL dd hh:mm:ss a')}</Content>
            <Title>Number Of Usage Extension</Title>
            <Content>{session?.number_of_usage_extensions}</Content>
            <Title>Start and No Enter</Title>
            <Content>
              {session?.actual_ended_at?.equals(session?.started_at) ? 'Yes' : 'No'}
            </Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div className="flex w-full justify-between">
            <CardTitle>Rating</CardTitle>

            <Link to={`/listings/${session.listing_id}#rating`}>
              <Button>View</Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>App Rating</Title>
            <Content>{session.listing_rating?.app_rating}</Content>
            <Title>Experience Rating</Title>
            <Content>{session.listing_rating?.experience_rating}</Content>
            <Title>Listing Rating</Title>
            <Content>{session.listing_rating?.listing_rating}</Content>
            <Title>Review</Title>
            <Content>{session.listing_rating?.review}</Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <div className="flex w-full justify-between">
            <CardTitle>Misc</CardTitle>

            <FormSheet title="Edit Misc" button="Edit Misc" edit formId="session-misc">
              <MiscForm session={session} />
            </FormSheet>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>User</Title>
            <Content>
              {session?.user?.full_name ??
                session?.user?.email_address ??
                session?.user?.username ??
                session?.user?.mobile_number ??
                session?.user?.id}
            </Content>
            <Title>Is Ended</Title>
            <Content>{session?.is_ended?.toString()}</Content>
            <Title>Is Hidden</Title>
            <Content>{session?.is_hidden?.toString()}</Content>
            <Title>Is Usage Extended</Title>
            <Content>{session?.is_usage_extended?.toString() ?? 'false'}</Content>
            <Title>Firestore Id</Title>
            <Content>{session?.firestore_id}</Content>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SessionCard
