import { FC, useContext } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import { Checkbox } from '@/components/ui/checkbox'
import { SheetContext } from '../Form/FormSheet'
import { Session, UpdateSession } from '@/types/Session'
import SessionService from '@/network/services/session'

const MiscForm: FC<{ session: Session }> = ({ session }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<UpdateSession>({
    shouldUseNativeValidation: false,
    defaultValues: {
      is_hidden: session.is_hidden
    }
  })

  const { handleSubmit, reset } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      await SessionService.clientUpdateHide(session.id, values)
      toast({
        title: 'Success',
        description: 'Update Session successfully.',
        action: <ToastAction altText="OK">OK</ToastAction>
      })
      mutate(
        (key) =>
          typeof key === 'string' &&
          key.startsWith(serialize(SessionService.getSingle(session.id), {}))
      )

      drawerContext.setIsOpen(false)
      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="session-misc">
        <FormField
          control={form.control}
          name="is_hidden"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
              <FormControl>
                <Checkbox checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Hide this session</FormLabel>
              </div>
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default MiscForm
