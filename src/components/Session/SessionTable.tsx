import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { Session, SessionResponse } from '@/types/Session'
import SessionService from '@/network/services/session'
import { DateTime } from 'luxon'
import { durationFormatter } from '@/lib/utils'

const columns: ColumnDef<Session>[] = [
  {
    accessorKey: 'listing.name',
    header: 'Listing'
  },
  {
    accessorKey: 'user.username',
    header: 'User',
    cell: (props) => {
      return (
        props.getValue<String>() ??
        props.row.original.user?.email_address ??
        props.row.original.user?.mobile_number ??
        props.row.original.user?.full_name ??
        props.row.original.user?.id
      )
    }
  },
  {
    accessorKey: 'started_at',
    header: 'Started At',
    cell: (props) => {
      const datetime = props.getValue<DateTime | null>()
      const datetimeISO = datetime ? datetime.toFormat('dd LLL yyyy hh:mm:ss a') : null

      return (
        <>
          <span
            style={{
              display: 'inline-block',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              marginRight: '8px',
              backgroundColor: datetimeISO ? '#F44336' : '#4CAF50'
            }}
          ></span>
          {datetimeISO ? datetimeISO : 'On-Going'}
        </>
      )
    }
  },
  {
    accessorKey: 'actual_ended_at',
    header: 'Ended At',
    cell: (props) => {
      const datetime = props.getValue<DateTime | null>()
      const datetimeISO = datetime ? datetime.toFormat('dd LLL yyyy hh:mm:ss a') : null

      return (
        <>
          {' '}
          <span
            style={{
              display: 'inline-block',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              marginRight: '8px',
              backgroundColor: datetimeISO ? '#F44336' : '#4CAF50'
            }}
          ></span>
          {datetimeISO ? datetimeISO : 'On-Going'}
        </>
      )
    }
  },
  {
    accessorKey: 'duration',
    header: 'Duration (m)',
    cell: (props) => {
      if (!props.row.original.actual_ended_at || !props.row.original.started_at) {
        return '-'
      }

      const diffInSeconds = props.row.original.actual_ended_at
        .diff(props.row.original.started_at, 'seconds')
        .toObject().seconds

      if (!diffInSeconds) {
        return '-'
      }

      return durationFormatter(diffInSeconds)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'listing.name',
    header: 'Listing',
    dataType: 'string',
    apiKey: 'listing'
  },
  {
    columnKey: 'user.username',
    header: 'User',
    dataType: 'string',
    apiKey: 'user' // Use 'user' as the API parameter name instead of 'user_username'
  }
]

const SessionTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<Session, unknown, SessionResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={SessionService.getAll}
        toRow={SessionService.toRow}
        toPaginate={SessionService.toPaginate}
        onRowClick={(row: Row<Session>) => {
          nav(`/sessions/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={['id', 'listing_id', 'user_id', 'actual_ended_at']}
      />
    </>
  )
}

export default SessionTable
