/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Navigate, Outlet, useMatches } from 'react-router-dom'
import useAuth from '@/hooks/useAuth'
import { SidebarInset, SidebarProvider } from './ui/sidebar'
import { AppSidebar } from './Sidebar/app-sidebar'
import { SiteHeader } from './Sidebar/site-header'
import { keys } from 'lodash'
import { PaginationAwareBreadcrumb } from './Breadcrumb/PaginationAwareBreadcrumb'
import { useDocumentTitle } from '@/hooks/useDocumentTitle'
import { useEffect } from 'react'

const DashboardLayout = () => {
  // Always call hooks at the top level, before any conditional logic
  const { user } = useAuth()
  const matches = useMatches()

  // Always call useDocumentTitle with a default value
  // This ensures the hook is always called in the same order
  useDocumentTitle('Dashboard')

  // Process matches to get breadcrumbs
  const matchesWithCrumbs = matches
    // @ts-ignore
    .filter((match) => Boolean(match.handle?.crumb))

  const crumbs = matchesWithCrumbs
    // @ts-ignore
    .map((match) => {
      const paramsKey = keys(match.params)
      const paramsValue = match.params[paramsKey[0]]
      // @ts-ignore
      const breadcrumb = match.handle.crumb(paramsValue)

      // Wrap the breadcrumb with PaginationAwareBreadcrumb to preserve pagination state
      return <PaginationAwareBreadcrumb>{breadcrumb}</PaginationAwareBreadcrumb>
    })

  // Helper function to extract text from React elements
  const extractTextFromReactElement = (element: any): string => {
    // If it's a string, return it directly
    if (typeof element === 'string') return element

    // If it's null or undefined, return empty string
    if (!element) return ''

    // If it's a React element with props.children
    if (element.props) {
      if (element.props.children) {
        // If children is a string, return it
        if (typeof element.props.children === 'string') {
          return element.props.children
        }

        // If children is an array, extract text from each child and join
        if (Array.isArray(element.props.children)) {
          return element.props.children
            .map((child: any) => extractTextFromReactElement(child))
            .join('')
        }

        // Recursively extract text from children
        return extractTextFromReactElement(element.props.children)
      }

      if (typeof element.props.label === 'string') {
        return element.props.label
      }
    }

    // Default fallback
    return ''
  }

  // Update document title based on breadcrumbs
  useEffect(() => {
    // Helper function to determine the page title
    const determinePageTitle = () => {
      // Check if we're on a detail page (has more than 2 breadcrumbs)
      if (crumbs.length > 2) {
        // For detail pages, use the parent section name (second breadcrumb)
        // This prevents using dynamic IDs as page titles
        const parentCrumb = crumbs[1] // The parent section breadcrumb
        if (parentCrumb) {
          const parentText = extractTextFromReactElement(parentCrumb)
          if (parentText) {
            return `${parentText} Detail`
          }
        }
      } else if (crumbs.length > 0) {
        // For section pages or dashboard, use the last breadcrumb
        const lastCrumb = crumbs[crumbs.length - 1]
        if (lastCrumb) {
          const extractedText = extractTextFromReactElement(lastCrumb)
          if (extractedText) {
            return extractedText
          }
        }
      }
      return 'Dashboard'
    }

    // Get the title and update document.title directly
    const pageTitle = determinePageTitle()
    const appName = 'GoMama'
    document.title = `${pageTitle} | ${appName}`
  }, [crumbs]) // Only re-run when crumbs change

  if (!user) {
    return <Navigate to="/" replace />
  }

  return (
    <SidebarProvider>
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader crumbs={crumbs} />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <Outlet />
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}

export default DashboardLayout
