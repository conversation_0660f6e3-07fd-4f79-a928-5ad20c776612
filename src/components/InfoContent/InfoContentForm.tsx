import { FC, useContext } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import { CreateInfoContentManagement, InfoContentManagement } from '@/types/InfoContentManagement'
import InfoContentService from '@/network/services/infoContent'
import EditorComponent from '../Editor/Editor'

const InfoContentForm: FC<{ infoContent?: InfoContentManagement }> = ({ infoContent }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<CreateInfoContentManagement>({
    shouldUseNativeValidation: false,
    defaultValues: infoContent
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!infoContent) {
        await InfoContentService.clientCreate(values)

        toast({
          title: 'Success',
          description: 'Create Info Content successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate((key) => typeof key === 'string' && key.startsWith(InfoContentService.getAll))
      } else {
        await InfoContentService.clientUpdate(infoContent.slug, values)

        toast({
          title: 'Success',
          description: 'Update Info Content successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(InfoContentService.getSingle(infoContent.slug), {}))
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="create-info">
        <FormField
          control={control}
          name="page_name"
          rules={{ required: 'Please enter the page name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Page Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="content"
          rules={{ required: 'Please enter the content' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Content</FormLabel>
              <FormControl>
                <EditorComponent content={field.value} onChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default InfoContentForm
