import { InfoContentManagement, InfoContentManagementResponse } from '@/types/InfoContentManagement'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import InfoContentService from '@/network/services/infoContent'
import { DateTime } from 'luxon'

const columns: ColumnDef<InfoContentManagement>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'page_name',
    header: 'Page Name'
  },
  {
    accessorKey: 'slug',
    header: 'Slug'
  },
  {
    accessorKey: 'created_by',
    header: 'Created By'
  },
  {
    accessorKey: 'updated_by',
    header: 'Updated By',
    cell: (props) => {
      return props.getValue<String>() ?? '-'
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'page_name',
    header: 'Page Name',
    dataType: 'string'
  },
  {
    columnKey: 'slug',
    header: 'Slug',
    dataType: 'string'
  }
]

const InfoContentTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<InfoContentManagement, unknown, InfoContentManagementResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={InfoContentService.getAll}
        toRow={InfoContentService.toRow}
        toPaginate={InfoContentService.toPaginate}
        onRowClick={(row: Row<InfoContentManagement>) => {
          nav(`/pages/${row.original.slug}`)
        }}
        sortParam="sort"
        sortColumns={[
          'id',
          'page_name',
          'slug',
          'created_by',
          'updated_by',
          'created_at',
          'updated_at'
        ]}
      />
    </>
  )
}

export default InfoContentTable
