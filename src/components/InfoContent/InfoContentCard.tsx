import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card'
import { InfoContentManagement } from '@/types/InfoContentManagement'
import { FC } from 'react'
import { Content, Title } from '../common'
import ReadOnlyEditorComponent from '../Editor/ReadOnlyEditor'
import { parseContent } from '@/lib/utils'
import { DateTime } from 'luxon'

const InfoContentCard: FC<{ infoContent?: InfoContentManagement }> = ({ infoContent }) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{infoContent?.id}</Content>
            <Title>Page Name</Title>
            <Content>{infoContent?.page_name}</Content>
            <Title>Slug</Title>
            <Content>{infoContent?.slug}</Content>
            <Title>Created By</Title>
            <Content>{infoContent?.created_by}</Content>
            <Title>Updated By</Title>
            <Content>{infoContent?.updated_by ?? '-'}</Content>
            <Title>Created At</Title>
            <Content>
              {infoContent?.created_at?.toLocaleString(DateTime.DATETIME_SHORT) ?? '-'}
            </Content>
            <Title>Updated At</Title>
            <Content>
              {infoContent?.updated_at?.toLocaleString(DateTime.DATETIME_SHORT) ?? '-'}
            </Content>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Content</CardTitle>
        </CardHeader>
        <CardContent>
          <ReadOnlyEditorComponent content={parseContent(infoContent?.content ?? '')} />
        </CardContent>
      </Card>
    </>
  )
}

export default InfoContentCard
