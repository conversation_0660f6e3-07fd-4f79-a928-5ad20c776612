'use client'

import * as React from 'react'
import {
  ActivitySquareIcon,
  AlarmClockIcon,
  DollarSignIcon,
  FlagIcon,
  HomeIcon,
  ImageIcon,
  InfoIcon,
  // LayoutDashboardIcon,
  ListIcon,
  MessagesSquareIcon,
  PlaySquareIcon,
  ScreenShareIcon,
  ScrollTextIcon,
  // SettingsIcon,
  UsersIcon
} from 'lucide-react'

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from '@/components/ui/sidebar'
import { NavMain } from './nav-main'
import { NavUser } from './nav-user'
import { NavSignage } from './nav-signage'
// import { NavSecondary } from './nav-secondary'
import { NavCommerce } from './nav-commerce'
import { NavCMS } from './nav-cms'
import { Link } from 'react-router-dom'

const data = {
  navMain: [
    // {
    //   title: 'Dashboard',
    //   url: '/dashboard',
    //   icon: LayoutDashboardIcon
    // },
    {
      title: 'Users',
      url: '/users',
      icon: UsersIcon
    },
    {
      title: 'Activities',
      url: '/activities',
      icon: ActivitySquareIcon
    },
    {
      title: 'Amenities',
      url: '/amenities',
      icon: HomeIcon
    },
    {
      title: 'Sessions',
      url: '/sessions',
      icon: AlarmClockIcon
    },
    {
      title: 'Listings',
      url: '/listings',
      icon: ListIcon
    },
    {
      title: 'Listing Flags',
      url: '/listing-flags',
      icon: FlagIcon
    },
    {
      title: 'Notifications',
      url: '/notification-messages',
      icon: MessagesSquareIcon
    }
  ],
  navSecondary: [],
  navCMS: [
    {
      name: 'Pages',
      url: '/pages',
      icon: ScrollTextIcon
    },
    {
      name: 'Announcements',
      url: '/announcements',
      icon: InfoIcon
    }
  ],
  navCommerce: [
    {
      name: 'Price Match Requests',
      url: '/price-match-requests',
      icon: DollarSignIcon
    },
    {
      name: 'Banners',
      url: '/shop-banners',
      icon: ImageIcon
    }
  ],
  navSignage: [
    {
      name: 'Devices',
      url: '/digital-signage-devices',
      icon: ScreenShareIcon
    },
    {
      name: 'Media',
      url: '/digital-signage-medias',
      icon: PlaySquareIcon
    }
  ]
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <Link to="/">
                <span className="text-base font-medium">Go!Mama</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavCMS items={data.navCMS} />
        <NavCommerce items={data.navCommerce} />
        <NavSignage items={data.navSignage} />
        {/* <NavSecondary items={data.navSecondary} className="mt-auto" /> */}
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
