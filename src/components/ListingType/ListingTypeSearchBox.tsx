import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import ListingTypeService from '@/network/services/listingType'
import { ListingType } from '@/types/ListingType'
import { useSearchListingType } from '@/hooks/search/useSearchListingType'

// multiple select
export const ListingTypeSearchBox: FC<{ id: string; name: string }> = ({ id, name }) => {
  const [open, setOpen] = useState(false)
  const [hasValue, setHasValue] = useState(false)
  const [listingType, setListingType] = useState<ListingType>()
  const form = useFormContext()

  useEffect(() => {
    const fetchListingType = async () => {
      const listingTypeData = await ListingTypeService.clientGetSingle(id)

      if (listingTypeData.data.data) {
        setListingType(listingTypeData.data.data)
      }
    }

    if (id) {
      fetchListingType()
    }
  }, [id])

  useEffect(() => {
    if (id) {
      setHasValue(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleSetActive = (listingType: ListingType) => {
    setListingType(listingType)
    form.setValue(name, listingType.id)
  }

  const displayName = !isEmpty(listingType) ? listingType?.name : 'Choose a listing type'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>ListingType</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(listingType) && 'text-muted-foreground',
                'w-full'
              )}
              disabled={hasValue}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<ListingType, ListingType>
            fn={useSearchListingType}
            renderFn={(prod: ListingType) => prod.name}
            valueFn={(prod: ListingType) => prod.name.toString()}
            compareFn={(prod: ListingType) => {
              if (isEmpty(listingType)) {
                return false
              }

              const findIndex = listingType?.name == prod.name

              return findIndex
            }}
            selectedResult={listingType}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
