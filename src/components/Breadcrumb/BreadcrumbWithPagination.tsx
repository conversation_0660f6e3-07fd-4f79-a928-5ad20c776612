import { useLocation } from 'react-router-dom'
import { Link } from 'react-router-dom'
import { BreadcrumbLink } from '@/components/ui/breadcrumb'
import { useEffect, memo } from 'react'

// Helper function to get the base path without query parameters
function getBasePath(path: string): string {
  return path.split('?')[0]
}

// Helper function to store pagination state in session storage
function storePaginationState(path: string, pageIndex?: string | null, pageSize?: string | null) {
  if (!pageIndex && !pageSize) return

  const basePath = getBasePath(path)
  const paginationState = {
    pageIndex,
    pageSize
  }

  sessionStorage.setItem(`pagination_${basePath}`, JSON.stringify(paginationState))
}

// Helper function to get pagination state from session storage
function getPaginationState(path: string) {
  const basePath = getBasePath(path)
  const storedState = sessionStorage.getItem(`pagination_${basePath}`)

  if (!storedState) return null

  try {
    return JSON.parse(storedState)
  } catch (e) {
    return null
  }
}

interface BreadcrumbWithPaginationProps {
  path: string
  label: string
}

// Create a memoized component to prevent unnecessary re-renders
const BreadcrumbWithPagination = memo(({ path, label }: BreadcrumbWithPaginationProps) => {
  // Always call hooks at the top level
  const location = useLocation()
  const basePath = getBasePath(path)

  // Store pagination state when on the table page
  useEffect(() => {
    // Only store pagination state if we're on the exact page (not a child route)
    if (getBasePath(location.pathname) === basePath) {
      const searchParams = new URLSearchParams(location.search)
      const pageIndex = searchParams.get('page')
      const pageSize = searchParams.get('size')

      if (pageIndex || pageSize) {
        storePaginationState(path, pageIndex, pageSize)
      }
    }
  }, [location, basePath, path])

  // Get pagination parameters from the current URL or session storage
  const searchParams = new URLSearchParams(location.search)
  let pageIndex = searchParams.get('page')
  let pageSize = searchParams.get('size')

  // If not in URL, try to get from session storage
  if (!pageIndex || !pageSize) {
    const storedState = getPaginationState(path)
    if (storedState) {
      pageIndex = pageIndex || storedState.pageIndex
      pageSize = pageSize || storedState.pageSize
    }
  }

  // Create a URL with pagination parameters if they exist
  let breadcrumbUrl = path
  const params = new URLSearchParams()

  if (pageIndex) {
    params.set('page', pageIndex)
  }

  if (pageSize) {
    params.set('size', pageSize)
  }

  // Add parameters to URL if any exist
  if (params.toString()) {
    breadcrumbUrl = `${path}?${params.toString()}`
  }

  // Return a breadcrumb link component
  return (
    <BreadcrumbLink asChild>
      <Link to={breadcrumbUrl}>{label}</Link>
    </BreadcrumbLink>
  )
})

// Set display name for debugging
BreadcrumbWithPagination.displayName = 'BreadcrumbWithPagination'

export default BreadcrumbWithPagination
