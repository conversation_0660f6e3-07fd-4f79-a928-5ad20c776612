interface TableLoadingOverlayProps {
  isLoading: boolean
}

export function TableLoadingOverlay({ isLoading }: TableLoadingOverlayProps) {
  if (!isLoading) return null

  return (
    <div className="absolute inset-0 bg-background/5 flex items-center justify-center pointer-events-none z-10">
      <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary opacity-50"></div>
    </div>
  )
}
