import { Cross2Icon, GearIcon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DataTableFacetedFilter } from '@/components/Table/DataTableFacetedFilter'
import { DataTableViewOptions } from '@/components/Table//DataTableViewOptions'
import { CalendarDatePicker } from '@/components/DatePicker/CalendarDatePicker'
import { FilterColumn } from './DataTable'
import { DateTime } from 'luxon'
import { useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { ListingStatus } from '@/types/Listing'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  filterColumns: FilterColumn[]
  isLoading?: boolean
}

export function DataTableToolbar<TData>({ table, filterColumns, isLoading = false }: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  // State to store filter values for nested properties
  const [nestedFilterValues, setNestedFilterValues] = useState<Record<string, string>>({});

  // Helper function to check if a column key is a nested property
  const isNestedProperty = (columnKey: string) => columnKey.includes('.')

  // Helper function to get the filter value (either from table column or local state)
  const getFilterValue = (columnKey: string) => {
    if (isNestedProperty(columnKey)) {
      return nestedFilterValues[columnKey] || ''
    }
    return (table.getColumn(columnKey)?.getFilterValue() as string) ?? ''
  }

  // Helper function to set the filter value
  const setFilterValue = (columnKey: string, value: string) => {
    if (isNestedProperty(columnKey)) {
      // For nested properties, store in local state and add to column filters manually
      setNestedFilterValues(prev => ({ ...prev, [columnKey]: value }))

      // Find existing filter index
      const filterIndex = table.getState().columnFilters.findIndex(f => f.id === columnKey)

      // Create new filters array
      const newFilters = [...table.getState().columnFilters]

      if (value) {
        // Add or update filter
        if (filterIndex >= 0) {
          newFilters[filterIndex] = { id: columnKey, value }
        } else {
          newFilters.push({ id: columnKey, value })
        }
      } else {
        // Remove filter if value is empty
        if (filterIndex >= 0) {
          newFilters.splice(filterIndex, 1)
        }
      }

      // Update column filters
      table.setColumnFilters(newFilters)
    } else {
      // For regular properties, use the table's built-in method
      table.getColumn(columnKey)?.setFilterValue(value)
    }
  }

  return (
    <div className="flex items-center justify-end space-x-2">
      <div className="flex flex-1 items-center space-x-2">
        {filterColumns.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto h-8" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-current"></div>
                    Filtering...
                  </>
                ) : (
                  <>
                    <GearIcon className="mr-2 h-4 w-4" />
                    Filter
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px] space-y-1 px-1">
              <DropdownMenuLabel>Filter columns</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {filterColumns.map((filterColumn) => {
                switch (filterColumn.dataType) {
                  case 'string':
                    return (
                      <Input
                        key={filterColumn.columnKey}
                        placeholder={filterColumn.header}
                        value={getFilterValue(filterColumn.columnKey)}
                        onChange={(event) => setFilterValue(filterColumn.columnKey, event.target.value)}
                        className="h-8 w-full"
                      />
                    )

                  case 'number':
                    return (
                      <Input
                        key={filterColumn.columnKey}
                        placeholder={filterColumn.header}
                        type="number"
                        value={getFilterValue(filterColumn.columnKey)}
                        onChange={(event) => setFilterValue(filterColumn.columnKey, event.target.value)}
                        className="h-8 w-full"
                      />
                    )

                  case 'date':
                    return (
                      <CalendarDatePicker
                        className="w-full"
                        key={filterColumn.columnKey}
                        buttonLabel={
                          isNestedProperty(filterColumn.columnKey)
                            ? nestedFilterValues[filterColumn.columnKey] || filterColumn.header
                            : (table.getColumn(filterColumn.columnKey)?.getFilterValue() as DateTime)?.toISODate() ?? filterColumn.header
                        }
                        mode="single"
                        onSelect={(e) => {
                          if (e) {
                            const dateTime = DateTime.fromISO(e.toISOString())
                            if (isNestedProperty(filterColumn.columnKey)) {
                              setFilterValue(filterColumn.columnKey, dateTime.toISO() || '')
                            } else {
                              table.getColumn(filterColumn.columnKey)?.setFilterValue(dateTime)
                            }
                            console.log('date changed', dateTime.toISO())
                          }
                        }}
                      />
                    )

                  case 'faceted':
                    return (
                      table.getColumn(filterColumn.columnKey) && (
                        <DataTableFacetedFilter
                          key={filterColumn.columnKey}
                          column={table.getColumn(filterColumn.columnKey)}
                          title={filterColumn.header}
                          options={filterColumn.options ?? []}
                        />
                      )
                    )

                  case 'listing_status_select':
                    return (
                      <Select
                        value={getFilterValue(filterColumn.columnKey)}
                        onValueChange={(value) => setFilterValue(filterColumn.columnKey, value)}
                      >
                        <SelectTrigger className="w-[190px]">
                          <SelectValue placeholder="Select Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Filter Status</SelectLabel>
                            {Object.keys(ListingStatus).map((status) => {
                              return (
                                <SelectItem value={status}>
                                  {ListingStatus[status as keyof typeof ListingStatus]}
                                </SelectItem>
                              )
                            })}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )

                  default:
                    return <p>unknown</p>
                }
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {/* Map the filters according to filterColumns */}
        {/* String variant */}
        {/* Number variant */}
        {/* Date variant */}
        {/* Faceted */}

        {/* String filters (string filter) */}
        {/* <Input
          placeholder="Filter"
          value={(table.getColumn('title')?.getFilterValue() as string) ?? ''}
          onChange={(event) => table.getColumn('title')?.setFilterValue(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />

        <Input
          placeholder="Location"
          value={(table.getColumn('location')?.getFilterValue() as string) ?? ''}
          onChange={(event) => table.getColumn('location')?.setFilterValue(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />

        <Input
          type="date"
          placeholder="Start Date"
          value={(table.getColumn('from')?.getFilterValue() as string) ?? ''}
          onChange={(event) => table.getColumn('from')?.setFilterValue(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        /> */}

        {/* Faceted filters (for data with limited options) */}
        {/* {table.getColumn('selected') && (
          <DataTableFacetedFilter
            column={table.getColumn('selected')}
            title="Selected"
            options={filterColumns[0].options ?? []}
          />
        )} */}

        {/* {table.getColumn("priority") && (
          <DataTableFacetedFilter
            column={table.getColumn("priority")}
            title="Priority"
            options={priorities}
          />
        )} */}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => {
              table.resetColumnFilters()
              // Reset nested filter values
              setNestedFilterValues({})
              // Reset to first page when filters are cleared
              table.setPageIndex(0)
            }}
            className="h-8 px-2 lg:px-3"
            disabled={isLoading}
          >
            Reset
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
