import React, { forwardRef, useImperativeHandle } from 'react'
import {
  ColumnDef,
  ColumnFiltersState,
  PaginationState,
  Row,
  RowSelectionState,
  SortingState,
  Table as TableType,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import { DataTablePagination } from '@/components/Table/DataTablePagination'
import { DataTableToolbar } from '@/components/Table/DataTableToolbar'
import { useEffect, useState } from 'react'
import { useTablefyWithPreload } from '@/hooks/useTablefyWithPreload'
import { fetcher as defaultFetcher } from '@/network/request'
import { cn, combineObjectAndArray } from '@/lib/utils'
import { ColumnFacetedOption } from './DataTableFacetedFilter'
import { serialize } from '@/network/request'
import { useDebounce } from 'use-debounce'
import { useTableStateWithUrl } from '@/hooks/useTableStateWithUrl'

import { ChevronDown, ChevronUp } from 'lucide-react'
import { TableSkeleton } from './TableSkeleton'
import { TableLoadingOverlay } from './TableLoadingOverlay'
import { Skeleton } from '@/components/ui/skeleton'

// Utility function to transform nested property filters
const transformNestedFilters = (filters: any[], filterColumns?: FilterColumn[]) => {
  if (!filters || filters.length === 0) return filters

  return filters.map(filter => {
    // Find the corresponding filter column definition
    const filterColumn = filterColumns?.find(col => col.columnKey === filter.id)

    // If apiKey is provided, use it directly
    if (filterColumn?.apiKey) {
      return { ...filter, id: filterColumn.apiKey }
    }

    // Check if the id contains a dot (indicating a nested property)
    if (filter.id.includes('.')) {
      const parts = filter.id.split('.')
      // For API compatibility, transform user.username to user_username by default
      const transformedId = `${parts[0]}_${parts[1]}`
      return { ...filter, id: transformedId }
    }

    return filter
  })
}

export type FilterColumn = {
  columnKey: string // column access key (must be same as column declaration)
  header: string // placeholder
  dataType?: 'string' | 'number' | 'faceted' | 'date' | 'listing_status_select' // To determine which type of filter to use
  options?: ColumnFacetedOption[] // list of faceted filter options
  apiKey?: string // Optional custom API parameter name for this filter
}

interface DataTableProps<TData, TValue, TResponseType> {
  columns: ColumnDef<TData, TValue>[]
  filterColumns?: FilterColumn[] // list of filterable columns
  swrService: string
  pageParam?: string
  limitParam?: string
  sortParam?: string
  sortColumns?: string[]
  defaultSort?: SortingState
  toRow: (data: TResponseType | undefined) => TData[]
  toPaginate: (data: TResponseType | undefined) => {
    total: number
    lastPage: number
  }
  setSelectedRows?: (selectedRows: number[]) => void
  initialSelected?: number[]
  onRowClick?: (row: Row<TData>, table: TableType<TData>) => void
  getSelectedRows?: (value: any) => void
  persistState?: boolean // Whether to persist pagination state in URL
}

export const DataTable = forwardRef(
  <TData, TValue, TResponseType>(
    {
      columns,
      filterColumns,
      swrService,
      pageParam = 'page', // API page query key
      limitParam = 'limit', // API limit query key
      sortParam, // API sort query key
      sortColumns,
      defaultSort,
      toRow,
      toPaginate,
      setSelectedRows,
      initialSelected,
      onRowClick,
      getSelectedRows,
      persistState = true // Enable state persistence by default
    }: DataTableProps<TData, TValue, TResponseType>,
    ref: React.Ref<any>
  ) => {
    // Use the URL-based pagination state if persistState is enabled
    const { paginationState, setPaginationState } = useTableStateWithUrl({
      defaultPageIndex: 0,
      defaultPageSize: 20
    })

    // Initialize with values from URL if persistState is enabled, otherwise use defaults
    const [pageSize, setPageSize] = useState(persistState ? paginationState.pageSize : 20)
    const [pageIndex, setPageIndex] = useState(persistState ? paginationState.pageIndex : 0)
    const [tableFilters, setTableFilters] = useState<ColumnFiltersState>([])
    const [isInitialLoading, setIsInitialLoading] = useState(true)

    const defaultSelected = initialSelected
      ? Object.fromEntries(initialSelected.map((item) => [item, true]))
      : {}

    /// Table states
    const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(defaultSelected)
    const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
    const [sorting, setSorting] = React.useState<SortingState>(defaultSort ? defaultSort : [])
    const [{ pageIndex: tablePageIndex, pageSize: tablePageSize }, setPagination] =
      React.useState<PaginationState>({
        pageIndex: persistState ? paginationState.pageIndex : pageIndex,
        pageSize: persistState ? paginationState.pageSize : pageSize
      })
    /// Table states
    // Debounce to prevent too many API calls while typing
    // Reduced from 1000ms to 500ms for better responsiveness
    const [debouncedFilter] = useDebounce(tableFilters, 500)

    const { tableData, totalPage, totalRow, isLoading, error } = useTablefyWithPreload<TData, TResponseType>({
      swrService: serialize(
        swrService,
        combineObjectAndArray(
          {
            [limitParam]: pageSize ?? 20,
            [pageParam]: pageParam === 'offset' ? pageIndex ?? 0 : pageIndex + 1,
            ...(sortParam &&
              sorting.length > 0 && {
                [sortParam]: `${sorting[0].id}:${sorting[0].desc ? 'DESC' : 'ASC'}`
              })
          },
          // Transform nested filters before sending to API
          transformNestedFilters(debouncedFilter, filterColumns)
        )
      ),
      pageParam,
      limitParam,
      pageIndex: pageIndex ?? 0,
      pageSize: pageSize ?? 20,
      sortParam,
      sorting,
      filters: transformNestedFilters(debouncedFilter, filterColumns),
      fetcher: defaultFetcher,
      toRow: toRow as (data: TResponseType | undefined) => TData[],
      toPaginate: toPaginate as (data: TResponseType | undefined) => {
        total: number
        lastPage: number
      }
    })

    // to change table pagination state
    const pagination = React.useMemo(
      () => ({
        pageIndex: tablePageIndex,
        pageSize: tablePageSize
      }),
      [tablePageIndex, tablePageSize]
    )

    const table = useReactTable({
      data: tableData,
      columns: columns,
      state: {
        sorting,
        columnVisibility,
        rowSelection,
        columnFilters,
        pagination // to update the table pagination state
      },
      manualPagination: true, // true since api handling pagination
      pageCount: totalPage ?? -1, // total pages from API (-1 if unknown / failed)
      enableRowSelection: true, // not working(?)

      onPaginationChange: setPagination,
      onRowSelectionChange: setRowSelection,
      onSortingChange: setSorting,
      onColumnFiltersChange: setColumnFilters,
      onColumnVisibilityChange: setColumnVisibility,

      manualFiltering: true, // true since api handling filtering
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      // Removed getFilteredRowModel to prevent local filtering
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getRowId: (row) => row['id' as keyof TData] as unknown as string // required for server pagination + row selection
    })

    // Change API limit
    useEffect(() => {
      setPageSize(tablePageSize)

      // Update URL state if persistState is enabled
      if (persistState) {
        setPaginationState(prev => ({
          ...prev,
          pageSize: tablePageSize
        }))
      }
    }, [tablePageSize, persistState, setPaginationState])

    // Change API page
    useEffect(() => {
      setPageIndex(pageParam === 'offset' ? tablePageSize * tablePageIndex : tablePageIndex)

      // Update URL state if persistState is enabled
      if (persistState) {
        setPaginationState(prev => ({
          ...prev,
          pageIndex: tablePageIndex
        }))
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [tablePageIndex, persistState, setPaginationState])

    // Table column filters
    useEffect(() => {
      console.log('filter change', columnFilters)

      // Reset to first page when filters change
      if (columnFilters.length > 0) {
        // Reset pagination state in the table
        setPagination((prev) => ({ ...prev, pageIndex: 0 }))
        // Reset the page index for API requests
        setPageIndex(0)
      }

      // Update filters for API request
      setTableFilters(columnFilters)
    }, [columnFilters])

    // Pass back an array of TData.id
    if (setSelectedRows) {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useEffect(() => {
        // convert map into array > setstate
        setSelectedRows(Object.keys(rowSelection).map(Number))
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, [rowSelection])
    }

    if (getSelectedRows) {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useEffect(() => {
        getSelectedRows(rowSelection)
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, [rowSelection])
    }

    const resetSelection = () => {
      table.resetRowSelection() // Reset row selection
    }

    // Update isInitialLoading state when data is loaded for the first time
    useEffect(() => {
      if (!isLoading && isInitialLoading) {
        setIsInitialLoading(false)
      }
    }, [isLoading, isInitialLoading])

    useImperativeHandle(ref, () => ({
      resetSelection
    }))

    // Show skeleton loading state only on initial load, not during filtering
    if (isLoading && isInitialLoading) {
      // Determine the number of columns to show in the skeleton
      const columnCount = columns.length || 5

      return (
        <TableSkeleton
          columnCount={columnCount}
          rowCount={10}
          showToolbar={true}
          showPagination={true}
        />
      )
    }

    // Only show full error page on initial load
    // For filtering errors, we'll show an error message in the table
    if (error && isInitialLoading) {
      // Show a skeleton with an error message overlay for consistency
      const columnCount = columns.length || 5

      return (
        <div className="space-y-4">
          <DataTableToolbar table={table} filterColumns={filterColumns ?? []} isLoading={false} />
          <div className="rounded-md border relative">
            {/* Semi-transparent skeleton in the background */}
            <div className="opacity-30">
              <Table>
                <TableHeader>
                  <TableRow>
                    {Array(columnCount)
                      .fill(null)
                      .map((_, index) => (
                        <TableHead key={index}>
                          <Skeleton className="h-6 w-full max-w-[120px]" />
                        </TableHead>
                      ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array(5)
                    .fill(null)
                    .map((_, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {Array(columnCount)
                          .fill(null)
                          .map((_, colIndex) => (
                            <TableCell key={colIndex}>
                              <Skeleton className="h-5 w-full" />
                            </TableCell>
                          ))}
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </div>

            {/* Error message overlay */}
            <div className="absolute inset-0 flex items-center justify-center bg-background/50">
              <div className="flex flex-col items-center space-y-2 p-8 text-center">
                <div className="text-sm font-medium text-destructive">Error loading data</div>
                <div className="text-xs text-muted-foreground">
                  Please try again or contact support
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        {/* Table filter */}
        <DataTableToolbar table={table} filterColumns={filterColumns ?? []} isLoading={isLoading} />

        {/* Table */}
        <div className="rounded-md border max-w-[calc(100vw-32px)] lg:max-w-auto relative">
          {/* Show loading overlay during filtering but not on initial load */}
          {isLoading && !isInitialLoading && <TableLoadingOverlay isLoading={true} />}

          {/* Show error overlay for filtering errors */}
          {error && !isInitialLoading && (
            <div className="absolute inset-0 bg-background/30 flex items-center justify-center z-10">
              <div className="bg-background p-4 rounded-md shadow-md border border-destructive/20">
                <div className="text-sm font-medium text-destructive">Error loading data</div>
                <div className="text-xs text-muted-foreground">Please try again</div>
              </div>
            </div>
          )}

          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        className="cursor-pointer"
                        onClick={() => {
                          if (sortColumns?.includes(header.column.id)) {
                            if (
                              table.getState().sorting.length > 0 &&
                              table.getState().sorting[0].id === header.column.id
                            ) {
                              // if same column sorting
                              switch (sorting[0].desc) {
                                case false:
                                  table.setSorting([{ id: header.column.id, desc: true }])
                                  break
                                case true:
                                default:
                                  table.setSorting([])
                                  break
                              }
                              // table.setSorting()
                            } else {
                              table.setSorting([{ id: header.column.id as string, desc: false }])
                            }
                          }
                        }}
                      >
                        <div className="flex flex-row items-center gap-x-2">
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                          {sortColumns?.includes(header.column.id) && (
                            <div className="flex flex-col">
                              <ChevronUp
                                size={12}
                                {...(sorting[0]?.id === header.column.id && {
                                  color: sorting[0].desc ? 'black' : undefined
                                })}
                              />
                              <ChevronDown
                                size={12}
                                {...(sorting[0]?.id === header.column.id && {
                                  color: !sorting[0].desc ? 'black' : undefined
                                })}
                              />
                            </div>
                          )}
                        </div>
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={() => {
                      onRowClick && onRowClick(row, table)
                    }}
                    className={cn(onRowClick != null ? 'cursor-pointer' : '')}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : isLoading ? (
                // Show loading rows instead of "No results found" when loading data
                Array(5).fill(null).map((_, rowIndex) => (
                  <TableRow key={`loading-row-${rowIndex}`}>
                    {Array(columns.length).fill(null).map((_, colIndex) => (
                      <TableCell key={`loading-cell-${rowIndex}-${colIndex}`}>
                        <div className="h-5 w-full animate-pulse rounded bg-muted/30"></div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center space-y-1">
                      <div className="text-sm text-muted-foreground">No results found</div>
                      <div className="text-xs text-muted-foreground">
                        Try adjusting your filters
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Table Pagination */}
        <DataTablePagination table={table} totalRow={totalRow} isLoading={isLoading} />
      </div>
    )
  }
) as <TData, TValue, TResponseType>(
  props: DataTableProps<TData, TValue, TResponseType> & { ref?: React.Ref<any> }
) => React.ReactElement
