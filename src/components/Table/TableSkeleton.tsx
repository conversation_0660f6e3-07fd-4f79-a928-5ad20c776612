import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

interface TableSkeletonProps {
  columnCount?: number
  rowCount?: number
  showToolbar?: boolean
  showPagination?: boolean
}

export function TableSkeleton({
  columnCount = 5,
  rowCount = 10,
  showToolbar = true,
  showPagination = true
}: TableSkeletonProps) {
  return (
    <div className="space-y-4">
      {/* Toolbar Skeleton */}
      {showToolbar && (
        <div className="flex items-center justify-between">
          <div className="flex-1 flex items-center space-x-2">
            <Skeleton className="h-8 w-[100px]" />
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-[70px]" />
          </div>
        </div>
      )}

      {/* Table Skeleton */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {Array(columnCount)
                .fill(null)
                .map((_, index) => (
                  <TableHead key={index}>
                    <Skeleton className="h-6 w-full max-w-[120px]" />
                  </TableHead>
                ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array(rowCount)
              .fill(null)
              .map((_, rowIndex) => (
                <TableRow key={rowIndex}>
                  {Array(columnCount)
                    .fill(null)
                    .map((_, colIndex) => (
                      <TableCell key={colIndex}>
                        <Skeleton
                          className="h-5"
                          style={{ width: `${Math.floor(Math.random() * 50) + 50}%` }}
                        />
                      </TableCell>
                    ))}
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Skeleton */}
      {showPagination && (
        <div className="flex flex-col md:flex-row md:items-center justify-between px-2 space-y-2 md:space-y-0">
          <Skeleton className="h-5 w-[200px]" />
          <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <Skeleton className="h-5 w-[120px]" />
              <Skeleton className="h-8 w-[70px]" />
            </div>
            <div className="flex items-center space-x-6 lg:space-x-8">
              <Skeleton className="h-5 w-[100px]" />
              <div className="flex items-center space-x-2">
                <Skeleton className="h-8 w-8 rounded-md" />
                <Skeleton className="h-8 w-8 rounded-md" />
                <Skeleton className="h-8 w-8 rounded-md" />
                <Skeleton className="h-8 w-8 rounded-md" />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
