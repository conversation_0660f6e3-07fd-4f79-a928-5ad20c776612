import React from 'react'
import { FileRejection, FileWithPath, useDropzone } from 'react-dropzone'

interface DropzoneProps {
  onDrop?: (acceptedFiles: FileWithPath[]) => void
  multiple?: boolean
  accept?: any
  description?: string
}

const Dropzone = React.forwardRef<HTMLInputElement, DropzoneProps>(
  (
    {
      onDrop,
      multiple = false,
      accept = { 'image/*': ['.jpeg', '.png', '.jpg'] },
      description,
      ...props
    },
    ref
  ) => {
    const innerRef = React.useRef<HTMLInputElement>(null)
    React.useImperativeHandle(ref, () => innerRef.current!, [])

    const { getRootProps, getInputProps, fileRejections } = useDropzone({
      onDrop: onDrop,
      accept,
      multiple,
      ...props
    })

    const fileRejectionItems = fileRejections.map((file: FileRejection, index: number) => {
      return (
        <li key={index}>
          <p>File: {(file.file as FileWithPath).path} error</p>
        </li>
      )
    })

    return (
      <>
        <section className="container flex flex-col items-center justify-center w-full h-20 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer">
          <div {...getRootProps({ className: 'dropzone w-full h-full' })}>
            <input {...getInputProps()} />
            <div className="flex h-full items-center justify-center text-greyLight">
              <p className="flex text-xs">
                {description ?? 'Drop your images here, or click to browse'}
              </p>
            </div>
          </div>
        </section>
        {fileRejectionItems.length > 0 && (
          <div>
            <h4>Rejected files</h4>
            <ul className={'text-redMain'}>{fileRejectionItems}</ul>
          </div>
        )}
      </>
    )
  }
)

Dropzone.displayName = 'Dropzone'

export { Dropzone }
