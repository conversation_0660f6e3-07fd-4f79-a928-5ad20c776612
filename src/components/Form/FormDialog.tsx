import { FC, PropsWithChildren, createContext } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { DrawerProps } from './schema'

export const DialogContext = createContext<DrawerProps | undefined>(undefined)

const FormDialog: FC<
  PropsWithChildren<{ title: string; open: boolean; setOpen: (val: boolean) => void }>
> = ({ children, title, open, setOpen }) => {
  return (
    <DialogContext.Provider
      value={{
        isOpen: open,
        setIsOpen: setOpen
      }}
    >
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-h-[90vh] max-w-xl overflow-y-scroll">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">{title}</DialogTitle>
          </DialogHeader>
          {children}
        </DialogContent>
      </Dialog>
    </DialogContext.Provider>
  )
}

export default FormDialog
