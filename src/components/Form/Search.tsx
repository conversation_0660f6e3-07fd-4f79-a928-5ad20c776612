/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react'
import { useDebounce } from 'use-debounce'

import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
  CommandGroup
} from '@/components/ui/command'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Check } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SearchFunction<TOption> {
  data: TOption[]
  pagination: { total: number; lastPage: number }
  isLoading: boolean
  error: any
}

interface SearchProps<TOption, TResult> {
  multiple?: boolean
  fn: (query: string) => SearchFunction<TOption>
  renderFn: (value: TOption) => string
  valueFn: (value: TOption) => string
  compareFn: (value: TOption) => boolean
  createFn?: (value: string) => void
  selectedResult?: TResult
  onSelectResult: (value: TOption) => void
  onDeselectResult?: (value: TOption) => void
  className?: string
  excludeId?: (string | number)[]
  useSlug?: boolean
}

interface SearchResultsProps<TOption, TResult> extends SearchProps<TOption, TResult> {
  query: string
  excludeId?: (string | number)[]
  useSlug?: boolean
}

export function Search<TOption, TResult>({
  multiple = false,
  fn,
  renderFn,
  valueFn,
  compareFn,
  createFn,
  selectedResult,
  onSelectResult,
  onDeselectResult,
  className,
  excludeId,
  useSlug
}: SearchProps<TOption, TResult>) {
  const [searchQuery, setSearchQuery] = React.useState('')

  const handleSelectResult = (result: TOption) => {
    onSelectResult(result)

    // OPTIONAL: reset the search query upon selection
    // setSearchQuery('');
  }

  const handleRemoveResult = (result: TOption) => {
    onDeselectResult && onDeselectResult(result)

    // OPTIONAL: reset the search query upon selection
    // setSearchQuery('');
  }

  return (
    <Command
      shouldFilter={false}
      className={cn('h-auto rounded-lg border border-b-0 shadow-md', className)}
    >
      <CommandInput value={searchQuery} onValueChange={setSearchQuery} placeholder="Search" />

      <SearchResults
        query={searchQuery}
        selectedResult={selectedResult}
        onSelectResult={handleSelectResult}
        onDeselectResult={handleRemoveResult}
        {...{ multiple, fn, renderFn, valueFn, compareFn, createFn, excludeId, useSlug }}
      />
    </Command>
  )
}

function SearchResults<TOption, TResult>({
  multiple,
  fn,
  renderFn,
  valueFn,
  compareFn,
  createFn,
  query,
  onSelectResult,
  onDeselectResult,
  excludeId,
  useSlug,
  selectedResult
}: SearchResultsProps<TOption, TResult>) {
  const [debouncedSearchQuery] = useDebounce(query, 250)

  const { data, error } = fn(debouncedSearchQuery)

  return (
    <CommandList>
      <CommandEmpty>No results found</CommandEmpty>

      {/* TODO: these should have proper loading aria */}
      {/* {isLoading && <div className="p-4 text-sm">Searching...</div>} */}
      {!error && (data?.length ?? 0) == 0 && (
        <>
          {createFn && debouncedSearchQuery != '' && (
            <CommandItem
              onSelect={() => createFn(debouncedSearchQuery)}
              className="px-4 py-3 text-sm"
            >
              Create {debouncedSearchQuery}
            </CommandItem>
          )}
        </>
      )}
      {error && <div className="p-4 text-sm">Something went wrong</div>}

      <CommandGroup>
        <ScrollArea>
          <div className="max-h-60">
            {data?.map((item, index) => {
              //@ts-ignore
              if (excludeId && excludeId.includes(item?.id) && item?.id != selectedResult?.id) {
                return <></>
              }

              if (
                useSlug &&
                excludeId &&
                //@ts-ignore
                excludeId.includes(item?.slug) &&
                //@ts-ignore
                item?.slug != selectedResult?.slug
              ) {
                return <></>
              }

              return (
                <CommandItem
                  key={index}
                  onSelect={() => {
                    if (multiple) {
                      if (compareFn(item)) {
                        onDeselectResult && onDeselectResult(item)
                        return
                      }
                    }

                    onSelectResult(item)
                  }}
                  value={valueFn(item)}
                  className="px-4 py-3 text-sm"
                >
                  <Check
                    className={cn('mr-2 h-4 w-4', compareFn(item) ? 'opacity-100' : 'opacity-0')}
                  />
                  {renderFn(item)}
                </CommandItem>
              )
            })}
          </div>
        </ScrollArea>
      </CommandGroup>
    </CommandList>
  )
}
