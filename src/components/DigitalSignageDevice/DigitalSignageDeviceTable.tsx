import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import { DateTime } from 'luxon'
import { DigitalSignageDevice, DigitalSignageDeviceResponse } from '@/types/DigitalSignageDevice'
import DigitalSignageDeviceService from '@/network/services/digitalSignageDevice'

const columns: ColumnDef<DigitalSignageDevice>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'firestore_id',
    header: 'Firestore ID'
  },
  {
    accessorKey: 'listing_id',
    header: 'Listing ID'
  },
  {
    accessorKey: 'created_by',
    header: 'Created By'
  },
  {
    accessorKey: 'updated_by',
    header: 'Last Updated By',
    cell: (props) => {
      return props.getValue<String>() ?? '-'
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: (props) => {
      return props.getValue<DateTime>().toLocaleString(DateTime.DATETIME_SHORT)
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'number'
  },
  {
    columnKey: 'name',
    header: 'Name',
    dataType: 'string'
  },
  {
    columnKey: 'firestore_id',
    header: 'Firestore ID',
    dataType: 'string'
  },
  {
    columnKey: 'listing_id',
    header: 'Listing ID',
    dataType: 'string'
  },
  {
    columnKey: 'created_by',
    header: 'Created By',
    dataType: 'string'
  },
  {
    columnKey: 'updated_by',
    header: 'Updated By',
    dataType: 'string'
  }
]

const DigitalSignageDeviceTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<DigitalSignageDevice, unknown, DigitalSignageDeviceResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={DigitalSignageDeviceService.getAll}
        toRow={DigitalSignageDeviceService.toRow}
        toPaginate={DigitalSignageDeviceService.toPaginate}
        onRowClick={(row: Row<DigitalSignageDevice>) => {
          nav(`/digital-signage-devices/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={[
          'id',
          'name',
          'firestore_id',
          'listing_id',
          'created_by',
          'updated_by',
          'created_at',
          'updated_at'
        ]}
      />
    </>
  )
}

export default DigitalSignageDeviceTable
