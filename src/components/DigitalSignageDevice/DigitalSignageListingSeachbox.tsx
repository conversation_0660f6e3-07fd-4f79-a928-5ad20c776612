import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import { useFormContext } from 'react-hook-form'
import { DigitalSignageListing } from '@/types/DigitalSignageListing'
import DigitalSignageListingService from '@/network/services/digitalSignageListing'
import { useSearchDigitalSignageListing } from '@/hooks/search/useSearchDigitalSignageListing'

// multiple select
export const DigitalSignageDeviceListingSearchBox: FC<{
  id: string
  name: string
}> = ({ id, name }) => {
  const [open, setOpen] = useState(false)
  const [listingState, setListing] = useState<DigitalSignageListing>()
  const form = useFormContext()

  useEffect(() => {
    const fetchData = async () => {
      const clientData = await DigitalSignageListingService.clientGetSingle(id)

      if (clientData.data.data) {
        setListing(clientData.data.data)
      }
    }

    if (id) {
      fetchData()
    }
  }, [id])

  const handleSetActive = (listing: DigitalSignageListing) => {
    setListing(listing)
    form.setValue(name, listing.id)
  }

  const displayName = !isEmpty(listingState)
    ? listingState?.gomama_core_listing_id
    : 'Choose a listing associated with the device'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Device Associated Listing ID</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(listingState) && 'text-muted-foreground',
                'w-full'
              )}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[500px]')}>
          <Search<DigitalSignageListing, DigitalSignageListing>
            fn={useSearchDigitalSignageListing}
            renderFn={(listing: DigitalSignageListing) => listing.gomama_core_listing_id}
            valueFn={(listing: DigitalSignageListing) => listing.id.toString()}
            compareFn={(listing: DigitalSignageListing) => {
              if (isEmpty(listingState)) {
                return false
              }

              const findIndex = listingState?.id == listing.id

              return findIndex
            }}
            selectedResult={listingState}
            onSelectResult={handleSetActive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
