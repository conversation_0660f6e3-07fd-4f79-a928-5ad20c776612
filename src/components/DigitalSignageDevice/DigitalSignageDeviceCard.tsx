import { Card, CardContent } from '@/components/ui/card'
import { FC } from 'react'
import { Content, Title } from '../common'
import { DigitalSignageDevice } from '@/types/DigitalSignageDevice'
import { DateTime } from 'luxon'

const DigitalSignageDeviceCard: FC<{ device?: DigitalSignageDevice }> = ({ device }) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>ID</Title>
            <Content>{device?.id}</Content>
            <Title>Name</Title>
            <Content>{device?.name}</Content>
            <Title>Device Setting Name</Title>
            <Content>{device?.device_setting?.name ?? '-'}</Content>
            <Title>Firestore ID</Title>
            <Content>{device?.firestore_id}</Content>
            <Title>Listing ID</Title>
            <Content>{device?.listing_id}</Content>
            <Title>Updated By</Title>
            <Content>{device?.updated_by ?? '-'}</Content>
            <Title>Created By</Title>
            <Content>{device?.created_by}</Content>
            <Title>Created At</Title>
            <Content>{device?.created_at?.toLocaleString(DateTime.DATETIME_SHORT)}</Content>
            <Title>Updated At</Title>
            <Content>{device?.updated_at?.toLocaleString(DateTime.DATETIME_SHORT)}</Content>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default DigitalSignageDeviceCard
