import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import { useFormContext } from 'react-hook-form'
import DigitalSignageDeviceSettingService from '@/network/services/digitalSignageDeviceSetting'
import { DigitalSignageDeviceSetting } from '@/types/DigitalSignageDeviceSetting'
import { useSearchDigitalSignageDeviceSetting } from '@/hooks/search/useSearchDigitalSignageDeviceSetting'

// multiple select
export const DigitalSignageDeviceSettingSearchBox: FC<{
  id: string
  name: string
}> = ({ id, name }) => {
  const [open, setOpen] = useState(false)
  const [deviceSetting, setDeviceSetting] = useState<DigitalSignageDeviceSetting>()
  const form = useFormContext()

  useEffect(() => {
    const fetchData = async () => {
      const clientData = await DigitalSignageDeviceSettingService.clientGetSingle(id)

      if (clientData.data.data) {
        setDeviceSetting(clientData.data.data)
      }
    }

    if (id) {
      fetchData()
    }
  }, [id])

  const handleSetActive = (setting: DigitalSignageDeviceSetting) => {
    setDeviceSetting(setting)
    form.setValue(name, setting.id)
  }

  const displayName = !isEmpty(deviceSetting) ? deviceSetting?.name : 'Choose a device setting'

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Device Associated Setting ID</FormLabel>

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(deviceSetting) && 'text-muted-foreground',
                'w-full'
              )}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<DigitalSignageDeviceSetting, DigitalSignageDeviceSetting>
            fn={useSearchDigitalSignageDeviceSetting}
            renderFn={(setting: DigitalSignageDeviceSetting) => setting.name}
            valueFn={(setting: DigitalSignageDeviceSetting) => setting.id.toString()}
            compareFn={(setting: DigitalSignageDeviceSetting) => {
              if (isEmpty(deviceSetting)) {
                return false
              }

              const findIndex = deviceSetting?.id == setting.id

              return findIndex
            }}
            selectedResult={deviceSetting}
            onSelectResult={handleSetActive}
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
