import { FC, useContext } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import { CreateDigitalSignageDevice, DigitalSignageDevice } from '@/types/DigitalSignageDevice'
import DigitalSignageDeviceService from '@/network/services/digitalSignageDevice'
import { DigitalSignageDeviceSettingSearchBox } from './DigitalSignageDeviceSettingSeachbox'
import { DigitalSignageDeviceListingSearchBox } from './DigitalSignageListingSeachbox'

const DigitalSignageDeviceForm: FC<{ device?: DigitalSignageDevice }> = ({ device }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<CreateDigitalSignageDevice>({
    shouldUseNativeValidation: false,
    defaultValues: device
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!device) {
        await DigitalSignageDeviceService.clientCreate({
          ...values
        })

        toast({
          title: 'Success',
          description: 'Create device successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) => typeof key === 'string' && key.startsWith(DigitalSignageDeviceService.getAll)
        )
      } else {
        await DigitalSignageDeviceService.clientUpdate(device.id, values)

        toast({
          title: 'Success',
          description: 'Update device successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(DigitalSignageDeviceService.getSingle(device.id), {}))
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="digital-signage-device">
        <FormField
          control={control}
          name="name"
          rules={{ required: 'Please enter the device name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Device Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="device_setting_id"
          rules={{ required: 'Please enter the device setting' }}
          render={({ field }) => {
            return (
              <DigitalSignageDeviceSettingSearchBox
                id={field.value ? field.value.toString() : ''}
                name={field.name}
              />
            )
          }}
        />

        <FormField
          control={control}
          name="firestore_id"
          rules={{ required: 'Please enter the device associated firestore id' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Device Associated Firestore ID</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="listing_id"
          rules={{ required: 'Please enter the device associated listing id' }}
          render={({ field }) => {
            return (
              <DigitalSignageDeviceListingSearchBox
                id={field.value ? field.value.toString() : ''}
                name={field.name}
              />
            )
          }}
        />
      </form>
    </Form>
  )
}

export default DigitalSignageDeviceForm
