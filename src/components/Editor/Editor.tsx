import { Toggle } from '@/components/ui/toggle'
import {
  FontBoldIcon,
  FontItalicIcon,
  HeadingIcon,
  LinkBreak2Icon,
  ListBulletIcon,
  QuoteIcon,
  StrikethroughIcon
} from '@radix-ui/react-icons'
import Placeholder from '@tiptap/extension-placeholder'
import { EditorProvider, JSONContent, useCurrentEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import CharacterCount from '@tiptap/extension-character-count'
import {
  CornerDownLeftIcon,
  LinkIcon,
  ListOrderedIcon,
  RedoIcon,
  RulerIcon,
  UndoIcon
} from 'lucide-react'
import { useCallback } from 'react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip'
import { parseContent } from '@/lib/utils'

interface EditorProps {
  content?: string
  placeholder?: string
  onChange?: (value: JSONContent | string) => void
}

const Toolbar = () => {
  const { editor } = useCurrentEditor()

  const setLink = useCallback(() => {
    if (!editor) {
      return
    }

    const previousUrl = editor.getAttributes('link').href
    const url = window.prompt('URL', previousUrl)

    // cancelled
    if (url === null) {
      return
    }

    // empty
    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run()

      return
    }

    // update link
    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run()
  }, [editor])

  if (!editor) {
    return
  }

  return (
    <div className="flex flex-wrap space-x-2">
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={false}
              disabled={!editor.can().chain().focus().undo().run()}
              onClick={() => editor.chain().focus().undo().run()}
            >
              <UndoIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Undo</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={false}
              disabled={!editor.can().chain().focus().redo().run()}
              onClick={() => editor.chain().focus().redo().run()}
            >
              <RedoIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Redo</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={editor.isActive('bold')}
              disabled={!editor.can().chain().focus().toggleBold().run()}
              onClick={() => editor.chain().focus().toggleBold().run()}
            >
              <FontBoldIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Bold</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={editor.isActive('italic')}
              disabled={!editor.can().chain().focus().toggleItalic().run()}
              onClick={() => editor.chain().focus().toggleItalic().run()}
            >
              <FontItalicIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Italic</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={editor.isActive('strike')}
              disabled={!editor.can().chain().focus().toggleStrike().run()}
              onClick={() => editor.chain().focus().toggleStrike().run()}
            >
              <StrikethroughIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Strike through</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={editor.isActive('bulletList')}
              disabled={!editor.can().chain().focus().toggleBulletList()}
              onClick={() => editor.chain().focus().toggleBulletList().run()}
            >
              <ListBulletIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Unordered list</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={editor.isActive('orderedList')}
              disabled={!editor.can().chain().focus().toggleOrderedList()}
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
            >
              <ListOrderedIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Ordered list</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={editor.isActive('blockquote')}
              disabled={!editor.can().chain().focus().toggleBlockquote()}
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
            >
              <QuoteIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Quote</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={editor.isActive('heading', { level: 2 })}
              disabled={!editor.can().chain().focus().toggleHeading({ level: 2 })}
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            >
              <HeadingIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Heading</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={false}
              disabled={false}
              onClick={() => editor.commands.setHorizontalRule()}
            >
              <RulerIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Add a line</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle pressed={false} disabled={false} onClick={setLink}>
              <LinkIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Attach link</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle
              pressed={false}
              disabled={!editor.isActive('link')}
              onClick={() => editor.chain().focus().unsetLink().run()}
            >
              <LinkBreak2Icon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Unattach link</TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger type="button">
            <Toggle pressed={false} disabled={false} onClick={() => editor.commands.setHardBreak()}>
              <CornerDownLeftIcon className="h-4 w-4" />
            </Toggle>
          </TooltipTrigger>
          <TooltipContent>Line break</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  )
}

const Count = () => {
  const { editor } = useCurrentEditor()

  if (!editor) {
    return
  }

  return (
    <div className="text-sm text-muted-foreground rounded-b-md border-b px-3 pb-2 bg-white">
      {editor.storage.characterCount.characters()} characters
    </div>
  )
}

const EditorComponent = ({ content = '', placeholder = '', onChange }: EditorProps) => {

  return (
    <div className="editor w-full rounded-md border">
      <EditorProvider
        slotBefore={
          <div className="border-b px-2 py-1 sticky top-0 z-10 rounded-t-md bg-white">
            <Toolbar />
          </div>
        }
        slotAfter={<Count />}
        extensions={[
          StarterKit,
          // CharacterCount.configure({
          //   limit: 10000
          // }),
          Placeholder.configure({
            placeholder
          }),
          Link.configure({
            protocols: ['https'],
            validate: (href) => /^https?:\/\//.test(href)
          }),
          CharacterCount.configure()
        ]}
        content={parseContent(content)}
        editorProps={{
          attributes: {
            class:
              'prose focus-visible:outline-none max-w-none placeholder:text-muted-foreground min-h-[60px] w-full bg-transparent px-3 py-2 text-sm disabled:cursor-not-allowed disabled:opacity-50'
          }
        }}
        onUpdate={({ editor }) => {
          // onChange && onChange(editor.getJSON())
          onChange && onChange(editor.getHTML())
          // onChange && onChange(editor.storage.markdown.getMarkdown())
        }}
      />
    </div>
  )
}

export const PlainTextEditorComponent = ({
  content = '',
  placeholder = '',
  onChange
}: EditorProps) => {
  return (
    <div className="editor w-full rounded-md border">
      <EditorProvider
        slotBefore={
          <div className="border-b px-2 py-1 sticky top-0 z-10 rounded-t-md bg-white">
            <Toolbar />
          </div>
        }
        slotAfter={<Count />}
        extensions={[
          StarterKit,
          // CharacterCount.configure({
          //   limit: 10000
          // }),
          Placeholder.configure({
            placeholder
          }),
          Link.configure({
            protocols: ['https'],
            validate: (href) => /^https?:\/\//.test(href)
          }),
          CharacterCount.configure()
        ]}
        content={content}
        editorProps={{
          attributes: {
            class:
              'prose focus-visible:outline-none max-w-none placeholder:text-muted-foreground min-h-[60px] w-full bg-transparent px-3 py-2 text-sm disabled:cursor-not-allowed disabled:opacity-50'
          }
        }}
        onUpdate={({ editor }) => {
          // onChange && onChange(editor.getJSON())
          // onChange && onChange(editor.getHTML())
          onChange && onChange(editor.getText())
        }}
      />
    </div>
  )
}

export default EditorComponent
