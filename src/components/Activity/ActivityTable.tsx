import { Activity, ActivityResponse } from '@/types/Activity'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import ActivityService from '@/network/services/activity'

const columns: ColumnDef<Activity>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'name',
    header: 'Name'
  },
  {
    accessorKey: 'description',
    header: 'Description'
  },
  {
    accessorKey: 'slug',
    header: 'Slug'
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'name',
    header: 'Name',
    dataType: 'string'
  },
  {
    columnKey: 'description',
    header: 'Description',
    dataType: 'string'
  },
  {
    columnKey: 'slug',
    header: 'Slug',
    dataType: 'string'
  }
]

const ActivityTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<Activity, unknown, ActivityResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={ActivityService.getAll}
        toRow={ActivityService.toRow}
        toPaginate={ActivityService.toPaginate}
        onRowClick={(row: Row<Activity>) => {
          nav(`/activities/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={[
          'id',
          'name',
          'description',
          'slug',
        ]}
      />
    </>
  )
}

export default ActivityTable
