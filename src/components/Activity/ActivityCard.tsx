import FormDialog from '@/components/Form/FormDialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Activity } from '@/types/Activity'
import { FC, useState } from 'react'
import ActivityForm from './ActivityForm'
import { Content, Title } from '@/components/common'

const ActivityCard: FC<{ activity?: Activity }> = ({ activity }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0">
          <CardTitle>{activity?.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-2">
            <Title>ID</Title>
            <Content>{activity?.id}</Content>
            <Title>Description</Title>
            <Content>{activity?.description}</Content>
            {activity?.image_url && (
              <>
                <Title>Image URL</Title>
                <div className="text-ellipsis overflow-hidden w-full">
                  <Content>{activity.image_url}</Content>
                </div>
              </>
            )}
            <Title>Is Hidden</Title>
            <Content>{activity?.is_hidden?.toString()}</Content>
            <Title>Slug</Title>
            <Content>{activity?.slug}</Content>
          </div>
        </CardContent>
      </Card>

      <FormDialog title="Edit Activity" {...{ open: isDialogOpen, setOpen: setIsDialogOpen }}>
        <ActivityForm activity={activity} />
      </FormDialog>
    </>
  )
}

export default ActivityCard
