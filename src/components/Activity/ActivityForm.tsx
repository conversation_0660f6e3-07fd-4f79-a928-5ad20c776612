import { ChangeEvent, FC, useContext, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import { Activity, CreateActivity } from '@/types/Activity'
import ActivityService from '@/network/services/activity'
import { Label } from '../ui/label'
import { Card } from '../ui/card'

const ActivityForm: FC<{ activity?: Activity }> = ({ activity }) => {
  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()
  const [imageType, setImageType] = useState('File')
  const [image, setImage] = useState('')
  const [file, setFile] = useState<File>()

  const form = useForm<CreateActivity>({
    shouldUseNativeValidation: false,
    defaultValues: activity
  })

  const { handleSubmit, reset, control } = form

  useEffect(() => {
    if (activity?.image_url) {
      setImage(activity.image_url)
    }
  }, [activity])

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    if (e.target.files && e.target.files[0]) {
      setImage(URL.createObjectURL(e.target.files[0]))
      setFile(e.target.files[0])
    }
  }

  const onSubmit = handleSubmit(async (values) => {
    try {
      if (!activity) {
        if (imageType == 'File' && file != undefined) {
          const formData = new FormData()
          formData.append('image_file', file)
          formData.append('name', values['name'])
          formData.append('description', values['description'])
          await ActivityService.clientCreate(formData as never)
        } else {
          await ActivityService.clientCreate(values)
        }

        toast({
          title: 'Success',
          description: 'Create Activity successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate((key) => typeof key === 'string' && key.startsWith(ActivityService.getAll))
      } else {
        if (file) {
          const formData = new FormData()
          formData.append('image_file', file)
          formData.append('name', values['name'])
          formData.append('description', values['description'])
          await ActivityService.clientUpdate(activity.id, formData as never)
        } else {
          await ActivityService.clientUpdate(activity.id, values)
        }

        toast({
          title: 'Success',
          description: 'Update Activity successfully.',
          action: <ToastAction altText="OK">OK</ToastAction>
        })
        drawerContext.setIsOpen(false)
        mutate(
          (key) =>
            typeof key === 'string' &&
            key.startsWith(serialize(ActivityService.getSingle(activity.id), {}))
        )
      }

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="activity-form">
        <FormField
          control={control}
          name="name"
          rules={{ required: 'Please enter the name' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="description"
          rules={{ required: 'Please enter the description' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <FormLabel>Image Type</FormLabel>
          <div className="flex gap-2">
            <div className="flex gap-2">
              <Input
                type="radio"
                id="file"
                name="image_type"
                value="File"
                onChange={(e) => setImageType(e.target.value)}
                className="h-6"
                defaultChecked
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">File</Label>
              </div>
            </div>

            <div className="flex gap-2">
              <Input
                type="radio"
                id="url"
                name="image_type"
                value="URL"
                onChange={(e) => setImageType(e.target.value)}
                className="h-6"
              />
              <div className="flex h-full content-center flex-wrap">
                <Label className="h-min">URL</Label>
              </div>
            </div>
          </div>
        </div>

        {imageType == 'File' && (
          <FormField
            control={control}
            name="image_file"
            rules={{ required: file || image != '' ? false : 'Please upload an image file' }}
            render={() => (
              <FormItem>
                <FormLabel>Activity Image</FormLabel>
                <FormControl>
                  <div className="w-48 h-80 relative mt-2">
                    <Card className="w-full h-full p-0  cursor-pointer">
                      {image != '' ? (
                        <img
                          src={image != '' ? image : ''}
                          alt="Image"
                          style={{ objectFit: 'contain', pointerEvents: 'none', height: '100%' }}
                        />
                      ) : (
                        <div
                          className="w-full h-full justify-center items-center absolute flex"
                          style={{ pointerEvents: 'none' }}
                        >
                          <div className="text-3xl">+</div>
                        </div>
                      )}
                      <input
                        type="file"
                        accept="image/*"
                        id="imageInput"
                        className=" w-full h-full cursor-pointer opacity-0 absolute top-0"
                        onChange={handleChange}
                      />
                    </Card>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {imageType == 'URL' && (
          <FormField
            control={control}
            name="image_url"
            rules={{ required: 'Please enter the image url' }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Image URL</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </form>
    </Form>
  )
}

export default ActivityForm
