import { cn } from '@/lib/utils'
import { ChevronsUpDown } from 'lucide-react'
import { isEmpty } from 'radash'
import { FC, useEffect, useState } from 'react'
import { FormItem, FormControl, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Search } from '@/components/Form/Search'
import ActivityService from '@/network/services/activity'
import { Activity } from '@/types/Activity'
import { useSearchActivity } from '@/hooks/search/useSearchActivity'

// multiple select
export const ActivitySearchBox: FC<{
  slug: string
  value: string[]
  onChange: (d: string[]) => void
  index: number
}> = ({ slug, value, onChange, index }) => {
  const [open, setOpen] = useState(false)
  const [activity, setActivity] = useState<Activity>()

  useEffect(() => {
    const fetchActivity = async () => {
      const activityData = await ActivityService.clientGetSingleSlug(slug)

      if (activityData.data.data) {
        setActivity(activityData.data.data)
      }
    }

    if (slug) {
      fetchActivity()
    }
  }, [slug])

  const handleSetActive = (activity: Activity) => {
    if (activity?.slug != slug) {
      setActivity(activity)
      const newValue = [...value]
      newValue[index] = activity.slug
      onChange(newValue)
    } else {
      setActivity(undefined)
      const newValue = [...value]
      newValue[index] = ''
      onChange(newValue)
    }
  }

  const displayName = !isEmpty(activity) ? activity?.name : 'Choose a activity'

  return (
    <FormItem className="flex flex-col pb-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button
              variant="outline"
              role="combobox"
              className={cn(
                'w-[200px] justify-between',
                isEmpty(activity) && 'text-muted-foreground',
                'w-full'
              )}
              // disabled={hasValue}
            >
              <>{displayName}</>

              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>

        <PopoverContent side="bottom" className={cn('p-0', 'w-[300px]')}>
          <Search<Activity, Activity>
            fn={useSearchActivity}
            renderFn={(prod: Activity) => `${prod.name} (${prod.slug})`}
            valueFn={(prod: Activity) => prod.slug}
            compareFn={(prod: Activity) => {
              if (isEmpty(activity)) {
                return false
              }

              const findIndex = activity?.slug == prod.slug

              return findIndex
            }}
            selectedResult={activity}
            onSelectResult={handleSetActive}
            // onDeselectResult={handleSetInactive}
            excludeId={value}
            useSlug
          />
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  )
}
