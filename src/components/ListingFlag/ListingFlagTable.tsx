import { ListingFlag, ListingFlagResponse } from '@/types/ListingFlag'
import { DataTable, FilterColumn } from '../Table/DataTable'
import { ColumnDef, Row } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'
import ListingFlagService from '@/network/services/listingFlag'
import { cn, statusToColor } from '@/lib/utils'

const columns: ColumnDef<ListingFlag>[] = [
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'listing_id',
    header: 'Listing ID'
  },
  {
    accessorKey: 'category',
    header: 'Category'
  },
  {
    accessorKey: 'action_by',
    header: 'Review Status',
    cell: (props) => {
      const value = props.getValue<string>()
      const color = statusToColor(value ? 'approved' : 'rejected')

      return (
        <div className="flex items-center space-x-2">
          <div className={cn('h-1.5 w-1.5 self-center rounded-full', color)} />
          <span className="capitalize">{value ? 'Reviewed' : 'Unreviewed'}</span>
        </div>
      )
    }
  }
]

const columnFilter: FilterColumn[] = [
  {
    columnKey: 'id',
    header: 'ID',
    dataType: 'string'
  },
  {
    columnKey: 'listing_id',
    header: 'Listing ID',
    dataType: 'string'
  },
  {
    columnKey: 'category',
    header: 'Category',
    dataType: 'string'
  }
]

const ListingFlagTable = () => {
  const nav = useNavigate()
  return (
    <>
      <DataTable<ListingFlag, unknown, ListingFlagResponse>
        columns={columns}
        filterColumns={columnFilter}
        swrService={ListingFlagService.getAll}
        toRow={ListingFlagService.toRow}
        toPaginate={ListingFlagService.toPaginate}
        onRowClick={(row: Row<ListingFlag>) => {
          nav(`/listing-flags/${row.original.id}`)
        }}
        sortParam="sort"
        sortColumns={['id', 'listing_id', 'category']}
      />
    </>
  )
}

export default ListingFlagTable
