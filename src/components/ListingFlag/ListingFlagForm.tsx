import { FC, useContext } from 'react'
import { useForm } from 'react-hook-form'
import { DrawerProps } from '../Form/schema'
import { SheetContext } from '@/components/Form/FormSheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ToastAction } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { serialize } from '@/network/request'
import { mutate } from 'swr'
import ListingFlagService from '@/network/services/listingFlag'
import {
  ListingFlag,
  ListingFlagAction,
  ListingFlagCategory,
  UpdateListingFlag
} from '@/types/ListingFlag'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '../ui/select'

const ListingFlagForm: FC<{ listingFlag?: ListingFlag }> = ({ listingFlag }) => {
  if (!listingFlag) return <></>

  const drawerContext = useContext(SheetContext) as DrawerProps
  const { toast } = useToast()

  const form = useForm<UpdateListingFlag>({
    shouldUseNativeValidation: false,
    defaultValues: {
      category: listingFlag.category as ListingFlagCategory,
      action: listingFlag.action as ListingFlagAction,
      action_reason: ''
    }
  })

  const { handleSubmit, reset, control } = form

  const onSubmit = handleSubmit(async (values) => {
    try {
      await ListingFlagService.clientUpdate(listingFlag.id, values)

      toast({
        title: 'Success',
        description: 'Update Listing Flag successfully.',
        action: <ToastAction altText="OK">OK</ToastAction>
      })
      drawerContext.setIsOpen(false)
      mutate(
        (key) =>
          typeof key === 'string' &&
          key.startsWith(serialize(ListingFlagService.getSingle(listingFlag.id), {}))
      )

      reset()
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  })

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className={cn('space-y-4')} id="review-listing-flag-form">
        <FormField
          control={control}
          name="action"
          rules={{ required: 'Please select an action taken' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Action</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={(value) => field.onChange(value)}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select event category tag" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Action</SelectLabel>
                      {Object.keys(ListingFlagAction).map((action) => {
                        return (
                          <SelectItem value={action}>
                            {ListingFlagAction[action as keyof typeof ListingFlagAction]}
                          </SelectItem>
                        )
                      })}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="action_reason"
          rules={{ required: 'Please enter the action reason' }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Action Reason</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Correct the category if needed</FormLabel>
              <FormControl>
                <Select value={field.value} onValueChange={(value) => field.onChange(value)}>
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select event category tag" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Category</SelectLabel>
                      {Object.keys(ListingFlagCategory).map((category) => {
                        return (
                          <SelectItem value={category}>
                            {ListingFlagCategory[category as keyof typeof ListingFlagCategory]}
                          </SelectItem>
                        )
                      })}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  )
}

export default ListingFlagForm
