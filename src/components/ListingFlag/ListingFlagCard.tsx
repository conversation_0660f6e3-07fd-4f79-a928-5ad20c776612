import { Card, CardContent } from '@/components/ui/card'
import { cn, statusToColor } from '@/lib/utils'
import { ListingFlag, ListingFlagAction, ListingFlagCategory } from '@/types/ListingFlag'
import { FC } from 'react'
import ImageCard from '../Common/ImageCard'
import { Content, Title } from '@/components/common'

const ListingFlagCard: FC<{ listingFlag?: ListingFlag }> = ({ listingFlag }) => {
  return (
    <>
      <Card>
        <CardContent>
          <div className="grid grid-cols-2 gap-2 pt-10">
            <Title>Is Reviewed</Title>
            <Content>
              {
                <div className="flex justify-end  space-x-2">
                  <div
                    className={cn(
                      'h-1.5 w-1.5 self-center rounded-full',
                      statusToColor(listingFlag?.action_by ? 'approved' : 'rejected')
                    )}
                  />
                  <span className="capitalize">
                    {listingFlag?.action_by ? 'Reviewed' : 'Unreviewed'}
                  </span>
                </div>
              }
            </Content>
            <Title>Lisiting Id</Title>
            <Content>{listingFlag?.listing_id}</Content>
            <Title>User Id</Title>
            <Content>{listingFlag?.user_id}</Content>
            <Title>Category</Title>
            <Content>
              {ListingFlagCategory[listingFlag?.category as keyof typeof ListingFlagCategory]}
            </Content>
            <Title>Reason</Title>
            <Content>{listingFlag?.reason}</Content>
            <Title>Action</Title>
            <Content>
              {ListingFlagAction[listingFlag?.action as keyof typeof ListingFlagAction]}
            </Content>
            <Title>Action Reason</Title>
            <Content>{listingFlag?.action_reason}</Content>
            <Title>Action By</Title>
            <Content>{listingFlag?.action_by}</Content>
          </div>
        </CardContent>
      </Card>

      {listingFlag?.reference_images && listingFlag?.reference_images.length > 0 && (
        <Card className="mt-3">
          <CardContent>
            <div className="grid grid-cols-1 gap-2 pt-10">
              <h1>Reference Images</h1>
              <div className="grid grid-cols-3">
                {listingFlag?.reference_images?.map((image_url) => (
                  <ImageCard image_url={image_url} />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  )
}

export default ListingFlagCard
