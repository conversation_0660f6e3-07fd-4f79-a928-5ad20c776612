import { FC, useState } from 'react'
import { Button } from '../ui/button'
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '../ui/alert-dialog'
import { useToast } from '../ui/use-toast'
import { AxiosResponse } from 'axios'
import { ToastAction } from '../ui/toast'
import { TrashIcon } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

const DeleteButton: FC<{
  id: string | number
  deleteService: (id: string | number) => Promise<AxiosResponse>
  name: string
  deleteSuccessRoute?: string
}> = ({ id, deleteService, name, deleteSuccessRoute = '/' }) => {
  const { toast } = useToast()
  const [isOpen, setIsOpen] = useState(false)
  const navigate = useNavigate()

  const handleDelete = async () => {
    try {
      await deleteService(id)

      toast({
        title: 'Success',
        description: `Delete ${name} successfully.`,
        action: <ToastAction altText="OK">OK</ToastAction>
      })

      navigate(deleteSuccessRoute)
    } catch (error) {
      console.error(error)
      toast({
        title: 'Failed',
        description: 'Something went wrong',
        variant: 'destructive'
      })
    }
  }

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        <TrashIcon size="16" className="mr-2" />
        Delete {name}
      </Button>

      <AlertDialog open={isOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {name}</AlertDialogTitle>
            <AlertDialogDescription>Are you sure to delete this {name} ?</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button onClick={() => setIsOpen(false)}>Cancel</Button>

            <Button onClick={handleDelete}>Confirm</Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export default DeleteButton
