{"name": "gomama-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-context-menu": "^2.2.11", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.11", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.3", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.3", "@radix-ui/react-use-callback-ref": "^1.0.1", "@tanstack/react-table": "^8.10.7", "@tiptap/extension-character-count": "^2.6.6", "@tiptap/extension-document": "^2.6.6", "@tiptap/extension-highlight": "^2.2.4", "@tiptap/extension-link": "^2.6.6", "@tiptap/extension-list-item": "^2.2.4", "@tiptap/extension-placeholder": "^2.2.4", "@tiptap/extension-task-item": "^2.2.4", "@tiptap/extension-task-list": "^2.2.4", "@tiptap/extension-text-style": "^2.2.4", "@tiptap/pm": "^2.2.4", "@tiptap/react": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@unpic/react": "^0.0.38", "axios": "^1.5.1", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "cmdk": "^0.2.1", "country-state-city": "^3.2.1", "date-fns": "^2.30.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^10.16.4", "graphql-request": "^6.1.0", "input-otp": "^1.4.2", "lodash": "^4.17.21", "lucide-react": "^0.288.0", "luxon": "^3.4.3", "next-themes": "^0.4.6", "qs": "^6.11.2", "radash": "^11.0.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.56.0", "react-resizable-panels": "^2.1.8", "react-router-dom": "^6.17.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "swr": "^2.2.4", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "tiptap-markdown": "^0.8.10", "use-debounce": "^9.0.4", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@types/lodash": "^4.14.200", "@types/luxon": "^3.3.3", "@types/node": "^20.8.7", "@types/qs": "^6.9.9", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}